{"version": 3, "names": ["getDefaultHeaderHeight", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocale", "React", "Platform", "StyleSheet", "jsx", "_jsx", "HeaderSegment", "props", "direction", "leftLabelLayout", "setLeftLabelLayout", "useState", "undefined", "titleLayout", "setTitleLayout", "handleTitleLayout", "e", "height", "width", "nativeEvent", "layout", "handleLeftLabelLayout", "progress", "modal", "onGoBack", "backHref", "headerTitle", "title", "headerLeft", "left", "headerRight", "right", "headerBackImage", "headerBackTitle", "headerBackButtonDisplayMode", "OS", "headerBackTruncatedTitle", "headerBackAccessibilityLabel", "headerBackTestID", "headerBackAllowFontScaling", "headerBackTitleStyle", "headerTitleContainerStyle", "headerLeftContainerStyle", "headerRightContainerStyle", "headerBackgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerStatusBarHeight", "styleInterpolator", "rest", "defaultHeight", "flatten", "headerHeight", "titleStyle", "leftButtonStyle", "leftLabelStyle", "rightButtonStyle", "backgroundStyle", "useMemo", "current", "next", "layouts", "header", "screen", "leftLabel", "href", "backImage", "accessibilityLabel", "testID", "allowFontScaling", "onPress", "label", "truncatedLabel", "labelStyle", "onLabelLayout", "screenLayout", "canGoBack", "Boolean", "onLayout"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderSegment.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EACtBC,MAAM,EACNC,gBAAgB,EAEhBC,WAAW,QACN,4BAA4B;AACnC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAEEC,QAAQ,EACRC,UAAU,QAEL,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAoBtB,OAAO,SAASC,aAAaA,CAACC,KAAY,EAAE;EAC1C,MAAM;IAAEC;EAAU,CAAC,GAAGR,SAAS,CAAC,CAAC;EAEjC,MAAM,CAACS,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAE1DC,SAAS,CAAC;EAEZ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,KAAK,CAACU,QAAQ,CAClDC,SACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAAoB,IAAK;IAClD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9CN,cAAc,CAAED,WAAW,IAAK;MAC9B,IACEA,WAAW,IACXI,MAAM,KAAKJ,WAAW,CAACI,MAAM,IAC7BC,KAAK,KAAKL,WAAW,CAACK,KAAK,EAC3B;QACA,OAAOL,WAAW;MACpB;MAEA,OAAO;QAAEI,MAAM;QAAEC;MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,qBAAqB,GAAIL,CAAoB,IAAK;IACtD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9C,IACEX,eAAe,IACfQ,MAAM,KAAKR,eAAe,CAACQ,MAAM,IACjCC,KAAK,KAAKT,eAAe,CAACS,KAAK,EAC/B;MACA;IACF;IAEAR,kBAAkB,CAAC;MAAEO,MAAM;MAAEC;IAAM,CAAC,CAAC;EACvC,CAAC;EAED,MAAM;IACJI,QAAQ;IACRF,MAAM;IACNG,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,WAAW,EAAEC,KAAK;IAClBC,UAAU,EAAEC,IAAI,GAAGL,QAAQ,GACtBjB,KAA4B,iBAAKF,IAAA,CAACP,gBAAgB;MAAA,GAAKS;IAAK,CAAG,CAAC,GACjEK,SAAS;IACbkB,WAAW,EAAEC,KAAK;IAClBC,eAAe;IACfC,eAAe;IACfC,2BAA2B,GAAGhC,QAAQ,CAACiC,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;IAC3EC,wBAAwB;IACxBC,4BAA4B;IAC5BC,gBAAgB;IAChBC,0BAA0B;IAC1BC,oBAAoB;IACpBC,yBAAyB;IACzBC,wBAAwB;IACxBC,yBAAyB;IACzBC,8BAA8B;IAC9BC,WAAW,EAAEC,iBAAiB;IAC9BC,qBAAqB;IACrBC,iBAAiB;IACjB,GAAGC;EACL,CAAC,GAAG1C,KAAK;EAET,MAAM2C,aAAa,GAAGtD,sBAAsB,CAC1CwB,MAAM,EACNG,KAAK,EACLwB,qBACF,CAAC;EAED,MAAM;IAAE9B,MAAM,GAAGiC;EAAc,CAAC,GAAG/C,UAAU,CAACgD,OAAO,CACnDL,iBAAiB,IAAI,CAAC,CACxB,CAAc;EAEd,MAAMM,YAAY,GAAG,OAAOnC,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGiC,aAAa;EAExE,MAAM;IACJG,UAAU;IACVC,eAAe;IACfC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC,GAAGxD,KAAK,CAACyD,OAAO,CACf,MACEV,iBAAiB,CAAC;IAChBW,OAAO,EAAE;MAAErC,QAAQ,EAAEA,QAAQ,CAACqC;IAAQ,CAAC;IACvCC,IAAI,EAAEtC,QAAQ,CAACsC,IAAI,IAAI;MAAEtC,QAAQ,EAAEA,QAAQ,CAACsC;IAAK,CAAC;IAClDpD,SAAS;IACTqD,OAAO,EAAE;MACPC,MAAM,EAAE;QACN7C,MAAM,EAAEmC,YAAY;QACpBlC,KAAK,EAAEE,MAAM,CAACF;MAChB,CAAC;MACD6C,MAAM,EAAE3C,MAAM;MACdO,KAAK,EAAEd,WAAW;MAClBmD,SAAS,EAAEvD;IACb;EACF,CAAC,CAAC,EACJ,CACEuC,iBAAiB,EACjB1B,QAAQ,EACRd,SAAS,EACT4C,YAAY,EACZhC,MAAM,EACNP,WAAW,EACXJ,eAAe,CAEnB,CAAC;EAED,MAAMmB,UAA4C,GAAGC,IAAI,GACpDtB,KAAK,IACJsB,IAAI,CAAC;IACH,GAAGtB,KAAK;IACR0D,IAAI,EAAExC,QAAQ;IACdyC,SAAS,EAAElC,eAAe;IAC1BmC,kBAAkB,EAAE9B,4BAA4B;IAChD+B,MAAM,EAAE9B,gBAAgB;IACxB+B,gBAAgB,EAAE9B,0BAA0B;IAC5C+B,OAAO,EAAE9C,QAAQ;IACjB+C,KAAK,EAAEtC,eAAe;IACtBuC,cAAc,EAAEpC,wBAAwB;IACxCqC,UAAU,EAAE,CAAClB,cAAc,EAAEf,oBAAoB,CAAC;IAClDkC,aAAa,EAAErD,qBAAqB;IACpCsD,YAAY,EAAEvD,MAAM;IACpBP,WAAW;IACX+D,SAAS,EAAEC,OAAO,CAACrD,QAAQ;EAC7B,CAAC,CAAC,GACJZ,SAAS;EAEb,MAAMkB,WAA8C,GAAGC,KAAK,GACvDxB,KAAK,IACJwB,KAAK,CAAC;IACJ,GAAGxB,KAAK;IACRqE,SAAS,EAAEC,OAAO,CAACrD,QAAQ;EAC7B,CAAC,CAAC,GACJZ,SAAS;EAEb,MAAMc,WAA8C,GAClD,OAAOC,KAAK,KAAK,UAAU,GACtBpB,KAAK,iBAAKF,IAAA,CAACN,WAAW;IAAA,GAAKQ,KAAK;IAAEuE,QAAQ,EAAE/D;EAAkB,CAAE,CAAC,GACjER,KAAK,IAAKoB,KAAK,CAAC;IAAE,GAAGpB,KAAK;IAAEuE,QAAQ,EAAE/D;EAAkB,CAAC,CAAC;EAEjE,oBACEV,IAAA,CAACR,MAAM;IACL0B,KAAK,EAAEA,KAAM;IACbH,MAAM,EAAEA,MAAO;IACfM,WAAW,EAAEA,WAAY;IACzBE,UAAU,EAAEA,UAAW;IACvBE,WAAW,EAAEA,WAAY;IACzBW,yBAAyB,EAAE,CAACY,UAAU,EAAEZ,yBAAyB,CAAE;IACnEC,wBAAwB,EAAE,CAACY,eAAe,EAAEZ,wBAAwB,CAAE;IACtEC,yBAAyB,EAAE,CAACa,gBAAgB,EAAEb,yBAAyB,CAAE;IACzET,2BAA2B,EAAEA,2BAA4B;IACzDU,8BAA8B,EAAE,CAC9Ba,eAAe,EACfb,8BAA8B,CAC9B;IACFC,WAAW,EAAEC,iBAAkB;IAC/BC,qBAAqB,EAAEA,qBAAsB;IAAA,GACzCE;EAAI,CACT,CAAC;AAEN", "ignoreList": []}