{"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "jsx", "_jsx", "CardA11yWrapper", "forwardRef", "focused", "active", "animated", "isNextScreenTransparent", "detachCurrentScreen", "children", "ref", "inert", "set<PERSON><PERSON><PERSON>", "useState", "useImperativeHandle", "isHidden", "pointerEvents", "style", "absoluteFill", "overflow", "undefined", "display", "OS", "visibility", "collapsable", "displayName"], "sourceRoot": "../../../../src", "sources": ["views/Stack/CardA11yWrapper.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAa1D,OAAO,MAAMC,eAAe,gBAAGN,KAAK,CAACO,UAAU,CAC7C,CACE;EACEC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,uBAAuB;EACvBC,mBAAmB;EACnBC;AACK,CAAC,EACRC,GAAkC,KAC/B;EACH;EACA;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;EAE/CjB,KAAK,CAACkB,mBAAmB,CAACJ,GAAG,EAAE,OAAO;IAAEE;EAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAExD,MAAMG,QAAQ,GACZ,CAACT,QAAQ,IACTC,uBAAuB,KAAK,KAAK,IACjCC,mBAAmB,KAAK,KAAK,IAC7B,CAACJ,OAAO;EAEV,oBACEH,IAAA,CAACF,IAAI;IACH,eAAa,CAACK,OAAQ;IACtBY,aAAa,EAAE,CAACV,QAAQ,GAAGK,KAAK,GAAG,CAACP,OAAO,IAAI,MAAM,GAAG,UAAW;IACnEa,KAAK,EAAE,CACLnB,UAAU,CAACoB,YAAY,EACvB;MACE;MACA;MACAC,QAAQ,EAAEd,MAAM,GAAGe,SAAS,GAAG,QAAQ;MACvC;MACAC,OAAO,EAAExB,QAAQ,CAACyB,EAAE,KAAK,KAAK,IAAIP,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC5D;MACA;MACA;MACAQ,UAAU,EAAER,QAAQ,GAAG,QAAQ,GAAG;IACpC,CAAC;IAEH;IACA;IAAA;IACAS,WAAW,EAAE,KAAM;IAAAf,QAAA,EAElBA;EAAQ,CACL,CAAC;AAEX,CACF,CAAC;AAEDP,eAAe,CAACuB,WAAW,GAAG,iBAAiB", "ignoreList": []}