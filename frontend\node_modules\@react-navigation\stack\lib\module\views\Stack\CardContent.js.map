{"version": 3, "names": ["React", "StyleSheet", "View", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "enabled", "layout", "style", "rest", "fill", "setFill", "useState", "useEffect", "document", "body", "width", "clientWidth", "height", "clientHeight", "isFullHeight", "id", "unsubscribe", "navigator", "maxTouchPoints", "getElementById", "createElement", "updateStyle", "vh", "window", "innerHeight", "textContent", "join", "head", "contains", "append<PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "remove", "pointerEvents", "styles", "page", "card", "create", "minHeight", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/Stack/CardContent.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAwB,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQhE;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,KAAK;EAAE,GAAGC;AAAY,CAAC,EAAE;EACtE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAAC,KAAK,CAAC;EAE7CZ,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MACrD;MACA;IACF;IAEA,MAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACE,WAAW;IACvC,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,IAAI,CAACI,YAAY;;IAEzC;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,YAAY,GAAGF,MAAM,KAAKX,MAAM,CAACW,MAAM;IAC7C,MAAMG,EAAE,GAAG,qDAAqD;IAEhE,IAAIC,WAAqC;IAEzC,IAAIF,YAAY,IAAIG,SAAS,CAACC,cAAc,GAAG,CAAC,EAAE;MAChD,MAAMhB,KAAK,GACTM,QAAQ,CAACW,cAAc,CAACJ,EAAE,CAAC,IAAIP,QAAQ,CAACY,aAAa,CAAC,OAAO,CAAC;MAEhElB,KAAK,CAACa,EAAE,GAAGA,EAAE;MAEb,MAAMM,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,EAAE,GAAGC,MAAM,CAACC,WAAW,GAAG,IAAI;QAEpCtB,KAAK,CAACuB,WAAW,GAAG,CAClB,iBAAiBH,EAAE,OAAO,EAC1B,8CAA8C,CAC/C,CAACI,IAAI,CAAC,IAAI,CAAC;MACd,CAAC;MAEDL,WAAW,CAAC,CAAC;MAEb,IAAI,CAACb,QAAQ,CAACmB,IAAI,CAACC,QAAQ,CAAC1B,KAAK,CAAC,EAAE;QAClCM,QAAQ,CAACmB,IAAI,CAACE,WAAW,CAAC3B,KAAK,CAAC;MAClC;MAEAqB,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAET,WAAW,CAAC;MAE9CL,WAAW,GAAGA,CAAA,KAAM;QAClBO,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAEV,WAAW,CAAC;MACnD,CAAC;IACH,CAAC,MAAM;MACL;MACA;MACAb,QAAQ,CAACW,cAAc,CAACJ,EAAE,CAAC,EAAEiB,MAAM,CAAC,CAAC;IACvC;;IAEA;IACA3B,OAAO,CAACK,KAAK,KAAKT,MAAM,CAACS,KAAK,IAAIE,MAAM,KAAKX,MAAM,CAACW,MAAM,CAAC;IAE3D,OAAOI,WAAW;EACpB,CAAC,EAAE,CAACf,MAAM,CAACW,MAAM,EAAEX,MAAM,CAACS,KAAK,CAAC,CAAC;EAEjC,oBACEZ,IAAA,CAACF,IAAI;IAAA,GACCO,IAAI;IACR8B,aAAa,EAAC,UAAU;IACxB/B,KAAK,EAAE,CAACF,OAAO,IAAII,IAAI,GAAG8B,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAElC,KAAK;EAAE,CAC7D,CAAC;AAEN;AAEA,MAAMgC,MAAM,GAAGvC,UAAU,CAAC0C,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb,CAAC;EACDF,IAAI,EAAE;IACJG,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}