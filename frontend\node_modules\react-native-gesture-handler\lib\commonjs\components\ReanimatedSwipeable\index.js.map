{"version": 3, "names": ["_ReanimatedSwipeableProps", "require", "_ReanimatedSwipeable", "_interopRequireDefault", "e", "__esModule", "default"], "sourceRoot": "../../../../src", "sources": ["components/ReanimatedSwipeable/index.ts"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AAKA,IAAAC,oBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgD,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA", "ignoreList": []}