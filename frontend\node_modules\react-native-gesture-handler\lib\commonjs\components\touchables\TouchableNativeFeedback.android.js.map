{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireWildcard", "React", "_GenericTouchable", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TouchableNativeFeedback", "Component", "defaultProps", "GenericTouchable", "useForeground", "extraButtonProps", "rippleColor", "SelectableBackground", "rippleRadius", "type", "attribute", "SelectableBackgroundBorderless", "<PERSON><PERSON><PERSON>", "color", "borderless", "canUseNativeForeground", "Platform", "OS", "Version", "getExtraButtonProps", "extraProps", "background", "props", "render", "style", "rest", "jsx", "exports"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableNativeFeedback.android.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA+B,IAAAG,KAAA,GAAAF,MAAA;AAE/B,IAAAG,iBAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAkD,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAK,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAMlD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMgB,uBAAuB,SAASC,gBAAS,CAA+B;EAC3F,OAAOC,YAAY,GAAG;IACpB,GAAGC,yBAAgB,CAACD,YAAY;IAChCE,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE;MAChB;MACAC,WAAW,EAAE;IACf;EACF,CAAC;;EAED;EACA,OAAOC,oBAAoB,GAAIC,YAAqB,KAAM;IACxDC,IAAI,EAAE,kBAAkB;IACxB;IACAC,SAAS,EAAE,0BAA0B;IACrCF;EACF,CAAC,CAAC;EACF,OAAOG,8BAA8B,GAAIH,YAAqB,KAAM;IAClEC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE,oCAAoC;IAC/CF;EACF,CAAC,CAAC;EACF,OAAOI,MAAM,GAAGA,CACdC,KAAiB,EACjBC,UAAmB,EACnBN,YAAqB,MACjB;IACJC,IAAI,EAAE,eAAe;IACrBI,KAAK;IACLC,UAAU;IACVN;EACF,CAAC,CAAC;EAEF,OAAOO,sBAAsB,GAAGA,CAAA,KAC9BC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAACE,OAAO,IAAI,EAAE;EAErDC,mBAAmBA,CAAA,EAAG;IACpB,MAAMC,UAA6C,GAAG,CAAC,CAAC;IACxD,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACC,KAAK;IACjC,IAAID,UAAU,EAAE;MACd;MACA;MACA,IAAIA,UAAU,CAACZ,IAAI,KAAK,eAAe,EAAE;QACvCW,UAAU,CAAC,YAAY,CAAC,GAAGC,UAAU,CAACP,UAAU;QAChDM,UAAU,CAAC,aAAa,CAAC,GAAGC,UAAU,CAACR,KAAK;MAC9C,CAAC,MAAM,IAAIQ,UAAU,CAACZ,IAAI,KAAK,kBAAkB,EAAE;QACjDW,UAAU,CAAC,YAAY,CAAC,GACtBC,UAAU,CAACX,SAAS,KAAK,oCAAoC;MACjE;MACA;MACAU,UAAU,CAAC,cAAc,CAAC,GAAGC,UAAU,CAACb,YAAY;IACtD;IACAY,UAAU,CAAC,YAAY,CAAC,GAAG,IAAI,CAACE,KAAK,CAAClB,aAAa;IACnD,OAAOgB,UAAU;EACnB;EACAG,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,KAAK,GAAG,CAAC,CAAC;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAACH,KAAK;IAC1C,oBACE,IAAA1C,WAAA,CAAA8C,GAAA,EAAChD,iBAAA,CAAAK,OAAgB;MAAA,GACX0C,IAAI;MACRD,KAAK,EAAEA,KAAM;MACbnB,gBAAgB,EAAE,IAAI,CAACc,mBAAmB,CAAC;IAAE,CAC9C,CAAC;EAEN;AACF;AAACQ,OAAA,CAAA5C,OAAA,GAAAiB,uBAAA", "ignoreList": []}