{"version": 3, "names": ["_createHandler", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "longPressGestureHandlerProps", "exports", "longPressHandlerName", "LongPressGestureHandler", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config", "shouldCancelWhenOutside"], "sourceRoot": "../../../src", "sources": ["handlers/LongPressGestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGgC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEzB,MAAMG,4BAA4B,GAAAC,OAAA,CAAAD,4BAAA,GAAG,CAC1C,eAAe,EACf,SAAS,EACT,kBAAkB,CACV;;AAuBV;AACA;AACA;;AAKO,MAAME,oBAAoB,GAAAD,OAAA,CAAAC,oBAAA,GAAG,yBAAyB;;AAE7D;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,MAAMC,uBAAuB,GAAAF,OAAA,CAAAE,uBAAA,GAAG,IAAAC,sBAAa,EAGlD;EACAC,IAAI,EAAEH,oBAAoB;EAC1BI,YAAY,EAAE,CACZ,GAAGC,6CAAuB,EAC1B,GAAGP,4BAA4B,CACvB;EACVQ,MAAM,EAAE;IACNC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC", "ignoreList": []}