{"version": 3, "names": ["_createHandler", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "rotationHandlerName", "exports", "RotationGestureHandler", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/RotationGestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGgC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhC;AACA;AACA;;AAIO,MAAMG,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,GAAG,wBAAwB;;AAE3D;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,MAAME,sBAAsB,GAAAD,OAAA,CAAAC,sBAAA,GAAG,IAAAC,sBAAa,EAGjD;EACAC,IAAI,EAAEJ,mBAAmB;EACzBK,YAAY,EAAEC,6CAAuB;EACrCC,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}