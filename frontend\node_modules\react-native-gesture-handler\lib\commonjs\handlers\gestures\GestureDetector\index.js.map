{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_findNodeHandle", "_interopRequireDefault", "_utils", "_GestureHandlerRootViewContext", "_useAnimatedGesture", "_attachHandlers", "_needsToReattach", "_dropHandlers", "_utils2", "_Wrap", "_useDetectorUpdater", "_useViewRefHandler", "_useMountReactions", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "propagateDetectorConfig", "props", "gesture", "keysToPropagate", "key", "value", "undefined", "g", "toGestureArray", "config", "GestureDetector", "rootViewContext", "useContext", "GestureHandlerRootViewContext", "__DEV__", "isTestEnv", "Platform", "OS", "Error", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "useMemo", "shouldUseReanimated", "some", "webEventHandlersRef", "useWebEventHandlers", "state", "useRef", "firstRender", "viewRef", "previousViewTag", "forceRebuildReanimatedEvent", "current", "preparedGesture", "React", "attachedGestures", "animatedEventHandler", "animatedHandlers", "isMounted", "updateAttachedGestures", "useDetectorUpdater", "ref<PERSON><PERSON><PERSON>", "useViewRefHandler", "needsToRebuildReanimatedEvent", "needsToReattach", "useAnimatedGesture", "useLayoutEffect", "viewTag", "findNodeHandle", "attachHandlers", "dropHandlers", "useEffect", "useMountReactions", "jsx", "AnimatedWrap", "ref", "onGestureHandlerEvent", "children", "Wrap", "exports"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/index.tsx"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AAIA,IAAAI,MAAA,GAAAJ,OAAA;AAEA,IAAAK,8BAAA,GAAAF,sBAAA,CAAAH,OAAA;AAEA,IAAAM,mBAAA,GAAAN,OAAA;AACA,IAAAO,eAAA,GAAAP,OAAA;AACA,IAAAQ,gBAAA,GAAAR,OAAA;AACA,IAAAS,aAAA,GAAAT,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AACA,IAAAa,kBAAA,GAAAb,OAAA;AACA,IAAAc,kBAAA,GAAAd,OAAA;AAAwD,IAAAe,WAAA,GAAAf,OAAA;AAAA,SAAAG,uBAAAa,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAjB,wBAAAiB,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAArB,uBAAA,YAAAA,CAAAiB,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAzBxD;;AA2BA,SAASgB,uBAAuBA,CAC9BC,KAA2B,EAC3BC,OAAsC,EACtC;EACA,MAAMC,eAA+C,GAAG,CACtD,YAAY,EACZ,mBAAmB,EACnB,aAAa,CACd;EAED,KAAK,MAAMC,GAAG,IAAID,eAAe,EAAE;IACjC,MAAME,KAAK,GAAGJ,KAAK,CAACG,GAAG,CAAC;IACxB,IAAIC,KAAK,KAAKC,SAAS,EAAE;MACvB;IACF;IAEA,KAAK,MAAMC,CAAC,IAAIL,OAAO,CAACM,cAAc,CAAC,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGF,CAAC,CAACE,MAAoC;MACrDA,MAAM,CAACL,GAAG,CAAC,GAAGC,KAAK;IACrB;EACF;AACF;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMK,eAAe,GAAIT,KAA2B,IAAK;EAC9D,MAAMU,eAAe,GAAG,IAAAC,iBAAU,EAACC,sCAA6B,CAAC;EACjE,IAAIC,OAAO,IAAI,CAACH,eAAe,IAAI,CAAC,IAAAI,gBAAS,EAAC,CAAC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACxE,MAAM,IAAIC,KAAK,CACb,qOACF,CAAC;EACH;;EAEA;EACA,MAAMC,aAAa,GAAGlB,KAAK,CAACC,OAAO;EACnCF,uBAAuB,CAACC,KAAK,EAAEkB,aAAa,CAAC;EAE7C,MAAMC,gBAAgB,GAAG,IAAAC,cAAO,EAC9B,MAAMF,aAAa,CAACX,cAAc,CAAC,CAAC,EACpC,CAACW,aAAa,CAChB,CAAC;EACD,MAAMG,mBAAmB,GAAGF,gBAAgB,CAACG,IAAI,CAC9ChB,CAAC,IAAKA,CAAC,CAACe,mBACX,CAAC;EAED,MAAME,mBAAmB,GAAG,IAAAC,2BAAmB,EAAC,CAAC;EACjD;EACA,MAAMC,KAAK,GAAG,IAAAC,aAAM,EAAuB;IACzCC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,IAAI;IACbC,eAAe,EAAE,CAAC,CAAC;IACnBC,2BAA2B,EAAE;EAC/B,CAAC,CAAC,CAACC,OAAO;EAEV,MAAMC,eAAe,GAAGC,cAAK,CAACP,MAAM,CAAuB;IACzDQ,gBAAgB,EAAE,EAAE;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,gBAAgB,EAAE,IAAI;IACtBf,mBAAmB,EAAEA,mBAAmB;IACxCgB,SAAS,EAAE;EACb,CAAC,CAAC,CAACN,OAAO;EAEV,MAAMO,sBAAsB,GAAG,IAAAC,sCAAkB,EAC/Cd,KAAK,EACLO,eAAe,EACfb,gBAAgB,EAChBD,aAAa,EACbK,mBACF,CAAC;EAED,MAAMiB,UAAU,GAAG,IAAAC,oCAAiB,EAAChB,KAAK,EAAEa,sBAAsB,CAAC;;EAEnE;EACA;EACA,MAAMI,6BAA6B,GACjCjB,KAAK,CAACE,WAAW,IACjBF,KAAK,CAACK,2BAA2B,IACjC,IAAAa,gCAAe,EAACX,eAAe,EAAEb,gBAAgB,CAAC;EACpDM,KAAK,CAACK,2BAA2B,GAAG,KAAK;EAEzC,IAAAc,sCAAkB,EAACZ,eAAe,EAAEU,6BAA6B,CAAC;EAElE,IAAAG,sBAAe,EAAC,MAAM;IACpB,MAAMC,OAAO,GAAG,IAAAC,uBAAc,EAACtB,KAAK,CAACG,OAAO,CAAW;IACvDI,eAAe,CAACK,SAAS,GAAG,IAAI;IAEhC,IAAAW,8BAAc,EAAC;MACbhB,eAAe;MACfd,aAAa;MACbC,gBAAgB;MAChBI,mBAAmB;MACnBuB;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXd,eAAe,CAACK,SAAS,GAAG,KAAK;MACjC,IAAAY,0BAAY,EAACjB,eAAe,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAkB,gBAAS,EAAC,MAAM;IACd,IAAIzB,KAAK,CAACE,WAAW,EAAE;MACrBF,KAAK,CAACE,WAAW,GAAG,KAAK;IAC3B,CAAC,MAAM;MACLW,sBAAsB,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACtC,KAAK,CAAC,CAAC;EAEX,IAAAmD,oCAAiB,EAACb,sBAAsB,EAAEN,eAAe,CAAC;EAE1D,IAAIX,mBAAmB,EAAE;IACvB,oBACE,IAAA1C,WAAA,CAAAyE,GAAA,EAAC7E,KAAA,CAAA8E,YAAY;MACXC,GAAG,EAAEd,UAAW;MAChBe,qBAAqB,EAAEvB,eAAe,CAACG,oBAAqB;MAAAqB,QAAA,EAC3DxD,KAAK,CAACwD;IAAQ,CACH,CAAC;EAEnB,CAAC,MAAM;IACL,oBAAO,IAAA7E,WAAA,CAAAyE,GAAA,EAAC7E,KAAA,CAAAkF,IAAI;MAACH,GAAG,EAAEd,UAAW;MAAAgB,QAAA,EAAExD,KAAK,CAACwD;IAAQ,CAAO,CAAC;EACvD;AACF,CAAC;AAACE,OAAA,CAAAjD,eAAA,GAAAA,eAAA", "ignoreList": []}