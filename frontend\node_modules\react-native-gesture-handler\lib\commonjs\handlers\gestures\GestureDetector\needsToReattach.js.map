{"version": 3, "names": ["needsToReattach", "preparedGesture", "newGestures", "length", "attachedGestures", "i", "handler<PERSON>ame", "shouldUseReanimated"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/needsToReattach.ts"], "mappings": ";;;;;;AAGA;AACA;AACA;AACA;AACO,SAASA,eAAeA,CAC7BC,eAAqC,EACrCC,WAA0B,EAC1B;EACA,IAAIA,WAAW,CAACC,MAAM,KAAKF,eAAe,CAACG,gBAAgB,CAACD,MAAM,EAAE;IAClE,OAAO,IAAI;EACb;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC3C,IACEH,WAAW,CAACG,CAAC,CAAC,CAACC,WAAW,KACxBL,eAAe,CAACG,gBAAgB,CAACC,CAAC,CAAC,CAACC,WAAW,IACjDJ,WAAW,CAACG,CAAC,CAAC,CAACE,mBAAmB,KAChCN,eAAe,CAACG,gBAAgB,CAACC,CAAC,CAAC,CAACE,mBAAmB,EACzD;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}