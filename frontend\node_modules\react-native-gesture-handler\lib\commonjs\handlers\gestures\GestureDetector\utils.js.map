{"version": 3, "names": ["_reactNative", "require", "_utils", "_gesture", "_FlingGestureHandler", "_ForceTouchGestureHandler", "_LongPressGestureHandler", "_PanGestureHandler", "_TapGestureHandler", "_hoverGesture", "_NativeViewGestureHandler", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_EnableNewWebImplementation", "_<PERSON><PERSON><PERSON><PERSON>", "_react", "_reanimated<PERSON><PERSON>per", "_eventReceiver", "ALLOWED_PROPS", "exports", "baseGestureHandlerWithDetectorProps", "tapGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "longPressGestureHandlerProps", "forceTouchGestureHandlerProps", "flingGestureHandlerProps", "hoverGestureHandlerProps", "nativeViewGestureHandlerProps", "convertToHandlerTag", "ref", "BaseGesture", "handlerTag", "current", "extractValidHandlerTags", "interactionGroup", "map", "filter", "tag", "extractGestureRelations", "gesture", "requireToFail", "config", "simultaneousWith", "blocksHandlers", "waitFor", "simultaneousHandlers", "checkGestureCallbacksForWorklets", "__DEV__", "runOnJS", "areSomeNotWorklets", "handlers", "isWorklet", "includes", "areSomeWorklets", "console", "error", "tagMessage", "Reanimated", "undefined", "areAllNotWorklets", "isTestEnv", "warn", "validateDetectorChildren", "Platform", "OS", "wrapType", "_reactInternals", "elementType", "instance", "<PERSON><PERSON><PERSON><PERSON>", "findHostInstance_DEPRECATED", "_internalFiberInstanceHandleDEV", "sibling", "Error", "return", "useForceRender", "renderState", "setRenderState", "useState", "forceRender", "useCallback", "useWebEventHandlers", "useRef", "onGestureHandlerEvent", "e", "nativeEvent", "onGestureHandlerStateChange", "isNewWebImplementationEnabled"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/utils.ts"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,yBAAA,GAAAJ,OAAA;AACA,IAAAK,wBAAA,GAAAL,OAAA;AACA,IAAAM,kBAAA,GAAAN,OAAA;AAIA,IAAAO,kBAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,yBAAA,GAAAT,OAAA;AACA,IAAAU,qBAAA,GAAAV,OAAA;AAIA,IAAAW,2BAAA,GAAAX,OAAA;AACA,IAAAY,WAAA,GAAAZ,OAAA;AACA,IAAAa,MAAA,GAAAb,OAAA;AACA,IAAAc,kBAAA,GAAAd,OAAA;AACA,IAAAe,cAAA,GAAAf,OAAA;AAGO,MAAMgB,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,CAC3B,GAAGE,yDAAmC,EACtC,GAAGC,yCAAsB,EACzB,GAAGC,yCAAsB,EACzB,GAAGC,qDAAkC,EACrC,GAAGC,qDAA4B,EAC/B,GAAGC,uDAA6B,EAChC,GAAGC,6CAAwB,EAC3B,GAAGC,sCAAwB,EAC3B,GAAGC,uDAA6B,CACjC;AAED,SAASC,mBAAmBA,CAACC,GAAe,EAAU;EACpD,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,YAAYC,oBAAW,EAAE;IACrC,OAAOD,GAAG,CAACE,UAAU;EACvB,CAAC,MAAM;IACL;IACA;IACA,OAAOF,GAAG,CAACG,OAAO,EAAED,UAAU,IAAI,CAAC,CAAC;EACtC;AACF;AAEA,SAASE,uBAAuBA,CAACC,gBAA0C,EAAE;EAC3E,OACEA,gBAAgB,EAAEC,GAAG,CAACP,mBAAmB,CAAC,EAAEQ,MAAM,CAAEC,GAAG,IAAKA,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE;AAE9E;AAEO,SAASC,uBAAuBA,CAACC,OAAoB,EAAE;EAC5D,MAAMC,aAAa,GAAGP,uBAAuB,CAACM,OAAO,CAACE,MAAM,CAACD,aAAa,CAAC;EAC3E,MAAME,gBAAgB,GAAGT,uBAAuB,CAC9CM,OAAO,CAACE,MAAM,CAACC,gBACjB,CAAC;EACD,MAAMC,cAAc,GAAGV,uBAAuB,CAACM,OAAO,CAACE,MAAM,CAACE,cAAc,CAAC;EAE7E,OAAO;IACLC,OAAO,EAAEJ,aAAa;IACtBK,oBAAoB,EAAEH,gBAAgB;IACtCC,cAAc,EAAEA;EAClB,CAAC;AACH;AAEO,SAASG,gCAAgCA,CAACP,OAAoB,EAAE;EACrE,IAAI,CAACQ,OAAO,EAAE;IACZ;EACF;EACA;EACA;EACA,IAAIR,OAAO,CAACE,MAAM,CAACO,OAAO,EAAE;IAC1B;EACF;EAEA,MAAMC,kBAAkB,GAAGV,OAAO,CAACW,QAAQ,CAACC,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMC,eAAe,GAAGd,OAAO,CAACW,QAAQ,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC;;EAEjE;EACA;EACA,IAAIH,kBAAkB,IAAII,eAAe,EAAE;IACzCC,OAAO,CAACC,KAAK,CACX,IAAAC,iBAAU,EACR,2QACF,CACF,CAAC;EACH;EAEA,IAAIC,6BAAU,KAAKC,SAAS,EAAE;IAC5B;IACA;EACF;EAEA,MAAMC,iBAAiB,GAAG,CAACN,eAAe,IAAIJ,kBAAkB;EAChE;EACA;EACA,IAAIU,iBAAiB,IAAI,CAAC,IAAAC,gBAAS,EAAC,CAAC,EAAE;IACrCN,OAAO,CAACO,IAAI,CACV,IAAAL,iBAAU,EACR,0OACF,CACF,CAAC;EACH;AACF;;AAEA;AACO,SAASM,wBAAwBA,CAACjC,GAAQ,EAAE;EACjD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIkB,OAAO,IAAIgB,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACpC;IACA,MAAMC,QAAQ;IACZ;IACApC,GAAG,CAACqC,eAAe,CAACC,WAAW;;IAEjC;IACA,IAAIC,QAAQ,GACVC,sBAAU,CAACC,2BAA2B,CACpCzC,GACF,CAAC,CAAC0C,+BAA+B;;IAEnC;IACA,OAAOH,QAAQ,IAAIA,QAAQ,CAACD,WAAW,KAAKF,QAAQ,EAAE;MACpD;MACA,IAAIG,QAAQ,CAACI,OAAO,EAAE;QACpB,MAAM,IAAIC,KAAK,CACb,mPACF,CAAC;MACH;;MAEA;MACAL,QAAQ,GAAGA,QAAQ,CAACM,MAAM;IAC5B;EACF;AACF;AAEO,SAASC,cAAcA,CAAA,EAAG;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG,IAAAC,eAAQ,EAAC,KAAK,CAAC;EACrD,MAAMC,WAAW,GAAG,IAAAC,kBAAW,EAAC,MAAM;IACpCH,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC,EAAE,CAACA,WAAW,EAAEC,cAAc,CAAC,CAAC;EAEjC,OAAOE,WAAW;AACpB;AAEO,SAASE,mBAAmBA,CAAA,EAAG;EACpC,OAAO,IAAAC,aAAM,EAAkB;IAC7BC,qBAAqB,EAAGC,CAAmC,IAAK;MAC9D,IAAAD,oCAAqB,EAACC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IACDC,2BAA2B,EAAE,IAAAC,yDAA6B,EAAC,CAAC,GACvDH,CAAmC,IAAK;MACvC,IAAAD,oCAAqB,EAACC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC,GACD3B;EACN,CAAC,CAAC;AACJ", "ignoreList": []}