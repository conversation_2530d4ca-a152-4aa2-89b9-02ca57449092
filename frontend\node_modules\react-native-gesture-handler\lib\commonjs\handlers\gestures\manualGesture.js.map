{"version": 3, "names": ["_gesture", "require", "changeEventCalculator", "current", "_previous", "ManualGesture", "ContinousBaseGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/manualGesture.ts"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AAEA,SAASC,qBAAqBA,CAC5BC,OAAkD,EAClDC,SAAqD,EACrD;EACA,SAAS;;EACT,OAAOD,OAAO;AAChB;AAEO,MAAME,aAAa,SAASC,6BAAoB,CAGrD;EACAC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,sBAAsB;EAC3C;EAEAC,QAAQA,CACNC,QAAoE,EACpE;IACA;IACA,IAAI,CAACC,QAAQ,CAACT,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF;AAACE,OAAA,CAAAP,aAAA,GAAAA,aAAA", "ignoreList": []}