{"version": 3, "names": ["_gesture", "require", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "changeX", "translationX", "changeY", "translationY", "PanGesture", "ContinousBaseGesture", "config", "constructor", "handler<PERSON>ame", "activeOffsetY", "offset", "Array", "isArray", "activeOffsetYStart", "activeOffsetYEnd", "activeOffsetX", "activeOffsetXStart", "activeOffsetXEnd", "failOffsetY", "failOffsetYStart", "failOffsetYEnd", "failOffsetX", "failOffsetXStart", "failOffsetXEnd", "minPointers", "maxPointers", "minDistance", "distance", "minDist", "minVelocity", "velocity", "minVelocityX", "minVelocityY", "averageTouches", "value", "avgTouches", "enableTrackpadTwoFingerGesture", "activateAfterLongPress", "duration", "onChange", "callback", "handlers", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/panGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAUA,SAASC,qBAAqBA,CAC5BC,OAA0D,EAC1DC,QAA4D,EAC5D;EACA,SAAS;;EACT,IAAIC,aAA2C;EAC/C,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,OAAO,EAAEJ,OAAO,CAACK,YAAY;MAC7BC,OAAO,EAAEN,OAAO,CAACO;IACnB,CAAC;EACH,CAAC,MAAM;IACLL,aAAa,GAAG;MACdE,OAAO,EAAEJ,OAAO,CAACK,YAAY,GAAGJ,QAAQ,CAACI,YAAY;MACrDC,OAAO,EAAEN,OAAO,CAACO,YAAY,GAAGN,QAAQ,CAACM;IAC3C,CAAC;EACH;EAEA,OAAO;IAAE,GAAGP,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEO,MAAMM,UAAU,SAASC,6BAAoB,CAGlD;EACOC,MAAM,GAAyC,CAAC,CAAC;EAExDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,mBAAmB;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACEC,aAAaA,CACXC,MAAuE,EACvE;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACJ,MAAM,CAACO,kBAAkB,GAAGH,MAAM,CAAC,CAAC,CAAC;MAC1C,IAAI,CAACJ,MAAM,CAACQ,gBAAgB,GAAGJ,MAAM,CAAC,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAACJ,MAAM,CAACO,kBAAkB,GAAGH,MAAM;IACzC,CAAC,MAAM;MACL,IAAI,CAACJ,MAAM,CAACQ,gBAAgB,GAAGJ,MAAM;IACvC;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEK,aAAaA,CACXL,MAAuE,EACvE;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACJ,MAAM,CAACU,kBAAkB,GAAGN,MAAM,CAAC,CAAC,CAAC;MAC1C,IAAI,CAACJ,MAAM,CAACW,gBAAgB,GAAGP,MAAM,CAAC,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAACJ,MAAM,CAACU,kBAAkB,GAAGN,MAAM;IACzC,CAAC,MAAM;MACL,IAAI,CAACJ,MAAM,CAACW,gBAAgB,GAAGP,MAAM;IACvC;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEQ,WAAWA,CACTR,MAAmE,EACnE;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACJ,MAAM,CAACa,gBAAgB,GAAGT,MAAM,CAAC,CAAC,CAAC;MACxC,IAAI,CAACJ,MAAM,CAACc,cAAc,GAAGV,MAAM,CAAC,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAACJ,MAAM,CAACa,gBAAgB,GAAGT,MAAM;IACvC,CAAC,MAAM;MACL,IAAI,CAACJ,MAAM,CAACc,cAAc,GAAGV,MAAM;IACrC;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEW,WAAWA,CACTX,MAAmE,EACnE;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACJ,MAAM,CAACgB,gBAAgB,GAAGZ,MAAM,CAAC,CAAC,CAAC;MACxC,IAAI,CAACJ,MAAM,CAACiB,cAAc,GAAGb,MAAM,CAAC,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAACJ,MAAM,CAACgB,gBAAgB,GAAGZ,MAAM;IACvC,CAAC,MAAM;MACL,IAAI,CAACJ,MAAM,CAACiB,cAAc,GAAGb,MAAM;IACrC;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEc,WAAWA,CAACA,WAAmB,EAAE;IAC/B,IAAI,CAAClB,MAAM,CAACkB,WAAW,GAAGA,WAAW;IACrC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACA,WAAmB,EAAE;IAC/B,IAAI,CAACnB,MAAM,CAACmB,WAAW,GAAGA,WAAW;IACrC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAgB,EAAE;IAC5B,IAAI,CAACrB,MAAM,CAACsB,OAAO,GAAGD,QAAQ;IAC9B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEE,WAAWA,CAACC,QAAgB,EAAE;IAC5B,IAAI,CAACxB,MAAM,CAACuB,WAAW,GAAGC,QAAQ;IAClC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEC,YAAYA,CAACD,QAAgB,EAAE;IAC7B,IAAI,CAACxB,MAAM,CAACyB,YAAY,GAAGD,QAAQ;IACnC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEE,YAAYA,CAACF,QAAgB,EAAE;IAC7B,IAAI,CAACxB,MAAM,CAAC0B,YAAY,GAAGF,QAAQ;IACnC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEG,cAAcA,CAACC,KAAc,EAAE;IAC7B,IAAI,CAAC5B,MAAM,CAAC6B,UAAU,GAAGD,KAAK;IAC9B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,8BAA8BA,CAACF,KAAc,EAAE;IAC7C,IAAI,CAAC5B,MAAM,CAAC8B,8BAA8B,GAAGF,KAAK;IAClD,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEG,sBAAsBA,CAACC,QAAgB,EAAE;IACvC,IAAI,CAAChC,MAAM,CAAC+B,sBAAsB,GAAGC,QAAQ;IAC7C,OAAO,IAAI;EACb;EAEAC,QAAQA,CACNC,QAIS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAAC9C,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAAC4C,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF;AAACE,OAAA,CAAAtC,UAAA,GAAAA,UAAA", "ignoreList": []}