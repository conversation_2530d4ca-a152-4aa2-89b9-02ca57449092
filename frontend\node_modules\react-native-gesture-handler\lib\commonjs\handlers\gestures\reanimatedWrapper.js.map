{"version": 3, "names": ["_utils", "require", "Reanimated", "exports", "e", "undefined", "useSharedValue", "setGestureState", "console", "warn", "tagMessage"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/reanimatedWrapper.ts"], "mappings": ";;;;;;AAKA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAIC,UAiBS,GAAAC,OAAA,CAAAD,UAAA;AAEb,IAAI;EACFC,OAAA,CAAAD,UAAA,GAAAA,UAAU,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACjD,CAAC,CAAC,OAAOG,CAAC,EAAE;EACV;EACA;EACAD,OAAA,CAAAD,UAAA,GAAAA,UAAU,GAAGG,SAAS;AACxB;AAEA,IAAI,CAACH,UAAU,EAAEI,cAAc,EAAE;EAC/B;EACA;EACAH,OAAA,CAAAD,UAAA,GAAAA,UAAU,GAAGG,SAAS;AACxB;AAEA,IAAIH,UAAU,KAAKG,SAAS,IAAI,CAACH,UAAU,CAACK,eAAe,EAAE;EAC3D;EACAL,UAAU,CAACK,eAAe,GAAG,MAAM;IACjC,SAAS;;IACTC,OAAO,CAACC,IAAI,CACV,IAAAC,iBAAU,EACR,gGACF,CACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}