{"version": 3, "names": ["_reactNative", "require", "_handlersRegistry", "_utils", "_RNGestureHandlerModule", "_interopRequireDefault", "_ghQueueMicrotask", "e", "__esModule", "default", "isConfigParam", "param", "name", "undefined", "Object", "filterConfig", "props", "validProps", "defaults", "filteredConfig", "key", "value", "transformIntoHandlerTags", "top", "left", "bottom", "right", "handlerIDs", "toArray", "Platform", "OS", "map", "current", "filter", "handle", "handlerID", "handlerIDToTag", "handlerTag", "findNodeHandle", "node", "findNodeHandleRN", "flushOperationsScheduled", "scheduleFlushOperations", "ghQueueMicrotask", "RNGestureHandlerModule", "flushOperations"], "sourceRoot": "../../../src", "sources": ["handlers/utils.ts"], "mappings": ";;;;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAAuD,SAAAI,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvD,SAASG,aAAaA,CAACC,KAAc,EAAEC,IAAY,EAAE;EACnD;EACA;EACA,OACED,KAAK,KAAKE,SAAS,KAClBF,KAAK,KAAKG,MAAM,CAACH,KAAK,CAAC,IACtB,EAAE,YAAY,IAAKA,KAAiC,CAAC,CAAC,IACxDC,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,gBAAgB;AAE7B;AAEO,SAASG,YAAYA,CAC1BC,KAA8B,EAC9BC,UAAoB,EACpBC,QAAiC,GAAG,CAAC,CAAC,EACtC;EACA,MAAMC,cAAc,GAAG;IAAE,GAAGD;EAAS,CAAC;EACtC,KAAK,MAAME,GAAG,IAAIH,UAAU,EAAE;IAC5B,IAAII,KAAK,GAAGL,KAAK,CAACI,GAAG,CAAC;IACtB,IAAIV,aAAa,CAACW,KAAK,EAAED,GAAG,CAAC,EAAE;MAC7B,IAAIA,GAAG,KAAK,sBAAsB,IAAIA,GAAG,KAAK,SAAS,EAAE;QACvDC,KAAK,GAAGC,wBAAwB,CAACN,KAAK,CAACI,GAAG,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIA,GAAG,KAAK,SAAS,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;QACzDA,KAAK,GAAG;UAAEE,GAAG,EAAEF,KAAK;UAAEG,IAAI,EAAEH,KAAK;UAAEI,MAAM,EAAEJ,KAAK;UAAEK,KAAK,EAAEL;QAAM,CAAC;MAClE;MACAF,cAAc,CAACC,GAAG,CAAC,GAAGC,KAAK;IAC7B;EACF;EACA,OAAOF,cAAc;AACvB;AAEO,SAASG,wBAAwBA,CAACK,UAAe,EAAE;EACxDA,UAAU,GAAG,IAAAC,cAAO,EAACD,UAAU,CAAC;EAEhC,IAAIE,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOH,UAAU,CACdI,GAAG,CAAC,CAAC;MAAEC;IAA0B,CAAC,KAAKA,OAAO,CAAC,CAC/CC,MAAM,CAAEC,MAAW,IAAKA,MAAM,CAAC;EACpC;EACA;EACA,OAAOP,UAAU,CACdI,GAAG,CACDI,SAAc,IACbC,gCAAc,CAACD,SAAS,CAAC,IAAIA,SAAS,CAACH,OAAO,EAAEK,UAAU,IAAI,CAAC,CACnE,CAAC,CACAJ,MAAM,CAAEI,UAAkB,IAAKA,UAAU,GAAG,CAAC,CAAC;AACnD;AAEO,SAASC,cAAcA,CAC5BC,IAA2E,EACJ;EACvE,IAAIV,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOS,IAAI;EACb;EACA,OAAO,IAAAC,2BAAgB,EAACD,IAAI,CAAC,IAAI,IAAI;AACvC;AACA,IAAIE,wBAAwB,GAAG,KAAK;AAE7B,SAASC,uBAAuBA,CAAA,EAAG;EACxC,IAAI,CAACD,wBAAwB,EAAE;IAC7BA,wBAAwB,GAAG,IAAI;IAC/B,IAAAE,kCAAgB,EAAC,MAAM;MACrBC,+BAAsB,CAACC,eAAe,CAAC,CAAC;MAExCJ,wBAAwB,GAAG,KAAK;IAClC,CAAC,CAAC;EACJ;AACF", "ignoreList": []}