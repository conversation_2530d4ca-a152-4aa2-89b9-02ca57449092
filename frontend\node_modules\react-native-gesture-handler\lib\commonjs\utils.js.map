{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "e", "__esModule", "default", "toArray", "object", "Array", "isArray", "withPrevAndCurrent", "array", "mapFn", "previousArr", "currentArr", "transformedArr", "for<PERSON>ach", "current", "i", "previous", "transformed", "push", "hasProperty", "key", "Object", "prototype", "hasOwnProperty", "call", "isTestEnv", "global", "process", "env", "NODE_ENV", "tagMessage", "msg", "isF<PERSON><PERSON>", "nativeFabricUIManager", "isReact19", "React", "version", "startsWith", "isRemoteDebuggingEnabled", "localGlobal", "nativeCallSyncHook", "__REMOTEDEV__", "RN$Bridgeless", "deepEqual", "obj1", "obj2", "keys1", "keys", "keys2", "length", "includes", "INT32_MAX", "exports"], "sourceRoot": "../../src", "sources": ["utils.ts"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEnB,SAASG,OAAOA,CAAIC,MAAe,EAAO;EAC/C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IAC1B,OAAO,CAACA,MAAM,CAAC;EACjB;EAEA,OAAOA,MAAM;AACf;AAMO,SAASG,kBAAkBA,CAChCC,KAAU,EACVC,KAA8C,EAC/B;EACf,MAAMC,WAAmC,GAAG,CAAC,IAAI,CAAC;EAClD,MAAMC,UAAU,GAAG,CAAC,GAAGH,KAAK,CAAC;EAC7B,MAAMI,cAA6B,GAAG,EAAE;EACxCD,UAAU,CAACE,OAAO,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAK;IACjC;IACA;IACA;IACA,MAAMC,QAAQ,GAAGN,WAAW,CAACK,CAAC,CAAuB;IACrD,MAAME,WAAW,GAAGR,KAAK,CAACO,QAAQ,EAAEF,OAAO,CAAC;IAC5CJ,WAAW,CAACQ,IAAI,CAACD,WAAW,CAAC;IAC7BL,cAAc,CAACM,IAAI,CAACD,WAAW,CAAC;EAClC,CAAC,CAAC;EACF,OAAOL,cAAc;AACvB;AAEO,SAASO,WAAWA,CAACf,MAAc,EAAEgB,GAAW,EAAE;EACvD,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACpB,MAAM,EAAEgB,GAAG,CAAC;AAC1D;AAEO,SAASK,SAASA,CAAA,EAAY;EACnC;EACA,OAAON,WAAW,CAACO,MAAM,EAAE,SAAS,CAAC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;AAC1E;AAEO,SAASC,UAAUA,CAACC,GAAW,EAAE;EACtC,OAAO,kCAAkCA,GAAG,EAAE;AAChD;;AAEA;AACA;AACO,SAASC,QAAQA,CAAA,EAAY;EAClC;EACA,OAAO,CAAC,CAACN,MAAM,EAAEO,qBAAqB;AACxC;AAEO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOC,cAAK,CAACC,OAAO,CAACC,UAAU,CAAC,KAAK,CAAC;AACxC;AAEO,SAASC,wBAAwBA,CAAA,EAAY;EAClD;EACA;EACA,MAAMC,WAAW,GAAGb,MAAa;EACjC,OACE,CAAC,CAACa,WAAW,CAACC,kBAAkB,IAAI,CAAC,CAACD,WAAW,CAACE,aAAa,KAC/D,CAACF,WAAW,CAACG,aAAa;AAE9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAACC,IAAS,EAAEC,IAAS,EAAE;EAC9C,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,IACE,OAAOD,IAAI,KAAK,QAAQ,IACxB,OAAOC,IAAI,KAAK,QAAQ,IACxBD,IAAI,KAAK,IAAI,IACbC,IAAI,KAAK,IAAI,EACb;IACA,OAAO,KAAK;EACd;EAEA,MAAMC,KAAK,GAAGzB,MAAM,CAAC0B,IAAI,CAACH,IAAI,CAAC;EAC/B,MAAMI,KAAK,GAAG3B,MAAM,CAAC0B,IAAI,CAACF,IAAI,CAAC;EAE/B,IAAIC,KAAK,CAACG,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,KAAK,MAAM7B,GAAG,IAAI0B,KAAK,EAAE;IACvB,IAAI,CAACE,KAAK,CAACE,QAAQ,CAAC9B,GAAG,CAAC,IAAI,CAACuB,SAAS,CAACC,IAAI,CAACxB,GAAG,CAAC,EAAEyB,IAAI,CAACzB,GAAG,CAAC,CAAC,EAAE;MAC5D,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEO,MAAM+B,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC", "ignoreList": []}