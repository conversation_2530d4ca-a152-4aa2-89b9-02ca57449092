{"version": 3, "names": ["_State", "require", "_constants", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "_ScaleGestureDetector", "e", "__esModule", "default", "PinchGestureHandler", "Gesture<PERSON>andler", "scale", "velocity", "startingSpan", "spanSlop", "DEFAULT_TOUCH_SLOP", "scaleDetectorListener", "onScaleBegin", "detector", "currentSpan", "onScale", "prevScaleFactor", "calculateScaleFactor", "tracker", "trackedPointersCount", "delta", "<PERSON><PERSON><PERSON><PERSON>", "Math", "abs", "state", "State", "BEGAN", "activate", "onScaleEnd", "_detector", "scaleGestureDetector", "ScaleGestureDetector", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "focalX", "focusX", "focalY", "focusY", "onPointerDown", "event", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "end", "fail", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "UNDETERMINED", "resetProgress", "begin", "force", "onReset", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/PinchGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAGA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAD,sBAAA,CAAAH,OAAA;AAE2C,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5B,MAAMG,mBAAmB,SAASC,uBAAc,CAAC;EACtDC,KAAK,GAAG,CAAC;EACTC,QAAQ,GAAG,CAAC;EAEZC,YAAY,GAAG,CAAC;EAChBC,QAAQ,GAAGC,6BAAkB;EAE7BC,qBAAqB,GAAyB;IACpDC,YAAY,EAAGC,QAA8B,IAAc;MACzD,IAAI,CAACL,YAAY,GAAGK,QAAQ,CAACC,WAAW;MACxC,OAAO,IAAI;IACb,CAAC;IACDC,OAAO,EAAGF,QAA8B,IAAc;MACpD,MAAMG,eAAuB,GAAG,IAAI,CAACV,KAAK;MAC1C,IAAI,CAACA,KAAK,IAAIO,QAAQ,CAACI,oBAAoB,CACzC,IAAI,CAACC,OAAO,CAACC,oBACf,CAAC;MAED,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,SAAS;MAChC,IAAID,KAAK,GAAG,CAAC,EAAE;QACb,IAAI,CAACb,QAAQ,GAAG,CAAC,IAAI,CAACD,KAAK,GAAGU,eAAe,IAAII,KAAK;MACxD;MAEA,IACEE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACf,YAAY,GAAGK,QAAQ,CAACC,WAAW,CAAC,IAAI,IAAI,CAACL,QAAQ,IACnE,IAAI,CAACe,KAAK,KAAKC,YAAK,CAACC,KAAK,EAC1B;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB;MACA,OAAO,IAAI;IACb,CAAC;IACDC,UAAU,EACRC,SAA+B,IAEtB,CAAC;EACd,CAAC;EAEOC,oBAAoB,GAAyB,IAAIC,6BAAoB,CAC3E,IAAI,CAACpB,qBACP,CAAC;EAEMqB,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAE;IAC3D,KAAK,CAACF,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;IAEzB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACtC;EAEUC,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACLC,MAAM,EAAE,IAAI,CAACP,oBAAoB,CAACQ,MAAM;MACxCC,MAAM,EAAE,IAAI,CAACT,oBAAoB,CAACU,MAAM;MACxCjC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBD,KAAK,EAAE,IAAI,CAACA;IACd,CAAC;EACH;EAEUmC,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACD,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACE,mBAAmB,CAACF,KAAK,CAAC;EACjC;EAEUG,YAAYA,CAACH,KAAmB,EAAQ;IAChD,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACD,KAAK,CAAC;IAChC,KAAK,CAACG,YAAY,CAACH,KAAK,CAAC;IACzB,IAAI,CAACI,QAAQ,CAAC,CAAC;IACf,IAAI,CAAChB,oBAAoB,CAACiB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;EAC7D;EAEU8B,WAAWA,CAACN,KAAmB,EAAQ;IAC/C,KAAK,CAACM,WAAW,CAACN,KAAK,CAAC;IACxB,IAAI,CAACxB,OAAO,CAAC+B,iBAAiB,CAACP,KAAK,CAACQ,SAAS,CAAC;IAC/C,IAAI,IAAI,CAAC1B,KAAK,KAAKC,YAAK,CAAC0B,MAAM,EAAE;MAC/B;IACF;IACA,IAAI,CAACrB,oBAAoB,CAACiB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;IAE3D,IAAI,IAAI,CAACM,KAAK,KAAKC,YAAK,CAAC0B,MAAM,EAAE;MAC/B,IAAI,CAACC,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACC,IAAI,CAAC,CAAC;IACb;EACF;EAEUC,eAAeA,CAACZ,KAAmB,EAAQ;IACnD,KAAK,CAACY,eAAe,CAACZ,KAAK,CAAC;IAC5B,IAAI,CAACZ,oBAAoB,CAACiB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;IAC3D,IAAI,CAACA,OAAO,CAAC+B,iBAAiB,CAACP,KAAK,CAACQ,SAAS,CAAC;IAE/C,IAAI,IAAI,CAAC1B,KAAK,KAAKC,YAAK,CAAC0B,MAAM,IAAI,IAAI,CAACjC,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACxE,IAAI,CAACiC,GAAG,CAAC,CAAC;IACZ;EACF;EAEUG,aAAaA,CAACb,KAAmB,EAAQ;IACjD,IAAI,IAAI,CAACxB,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IACA,IAAI,CAACD,OAAO,CAACsC,KAAK,CAACd,KAAK,CAAC;IAEzB,IAAI,CAACZ,oBAAoB,CAACiB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;IAC3D,KAAK,CAACqC,aAAa,CAACb,KAAK,CAAC;EAC5B;EACUe,oBAAoBA,CAACf,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAACxB,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IACA,IAAI,CAACD,OAAO,CAACsC,KAAK,CAACd,KAAK,CAAC;IAEzB,IAAI,CAACZ,oBAAoB,CAACiB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;IAC3D,KAAK,CAACuC,oBAAoB,CAACf,KAAK,CAAC;EACnC;EAEQI,QAAQA,CAAA,EAAS;IACvB,IAAI,IAAI,CAACtB,KAAK,KAAKC,YAAK,CAACiC,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEOjC,QAAQA,CAACkC,KAAe,EAAQ;IACrC,IAAI,IAAI,CAACrC,KAAK,KAAKC,YAAK,CAAC0B,MAAM,EAAE;MAC/B,IAAI,CAACQ,aAAa,CAAC,CAAC;IACtB;IAEA,KAAK,CAAChC,QAAQ,CAACkC,KAAK,CAAC;EACvB;EAEUC,OAAOA,CAAA,EAAS;IACxB,IAAI,CAACH,aAAa,CAAC,CAAC;EACtB;EAEUA,aAAaA,CAAA,EAAS;IAC9B,IAAI,IAAI,CAACnC,KAAK,KAAKC,YAAK,CAAC0B,MAAM,EAAE;MAC/B;IACF;IACA,IAAI,CAAC5C,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACD,KAAK,GAAG,CAAC;EAChB;AACF;AAACyD,OAAA,CAAA5D,OAAA,GAAAC,mBAAA", "ignoreList": []}