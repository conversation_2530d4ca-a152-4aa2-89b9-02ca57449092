{"version": 3, "names": ["_State", "require", "_interfaces", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "e", "__esModule", "default", "DEFAULT_MAX_DURATION_MS", "DEFAULT_MAX_DELAY_MS", "DEFAULT_NUMBER_OF_TAPS", "DEFAULT_MIN_NUMBER_OF_POINTERS", "TapGestureHandler", "Gesture<PERSON>andler", "maxDeltaX", "Number", "MIN_SAFE_INTEGER", "maxDeltaY", "maxDistSq", "maxDurationMs", "max<PERSON>elay<PERSON>", "numberOfTaps", "minNumberOfPointers", "currentMaxNumberOfPointers", "startX", "startY", "offsetX", "offsetY", "lastX", "lastY", "tapsSoFar", "updateGestureConfig", "enabled", "props", "config", "undefined", "maxDist", "minPointers", "resetConfig", "clearTimeouts", "clearTimeout", "waitTimeout", "delayTimeout", "startTap", "setTimeout", "fail", "endTap", "activate", "updateLastCoords", "x", "y", "tracker", "getAbsoluteCoordsAverage", "onPointerDown", "event", "isButtonInConfig", "button", "addToTracker", "trySettingPosition", "updateState", "tryToSendTouchEvent", "onPointerAdd", "onPointerUp", "removeFromTracker", "pointerId", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "trackedPointersCount", "shouldFail", "state", "State", "UNDETERMINED", "eventType", "EventTypes", "DOWN", "begin", "BEGAN", "UP", "dx", "Math", "abs", "dy", "distSq", "end", "onCancel", "resetProgress", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/TapGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAEA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA8C,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,uBAAuB,GAAG,GAAG;AACnC,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,8BAA8B,GAAG,CAAC;AAEzB,MAAMC,iBAAiB,SAASC,uBAAc,CAAC;EACpDC,SAAS,GAAGC,MAAM,CAACC,gBAAgB;EACnCC,SAAS,GAAGF,MAAM,CAACC,gBAAgB;EACnCE,SAAS,GAAGH,MAAM,CAACC,gBAAgB;EACnCG,aAAa,GAAGX,uBAAuB;EACvCY,UAAU,GAAGX,oBAAoB;EAEjCY,YAAY,GAAGX,sBAAsB;EACrCY,mBAAmB,GAAGX,8BAA8B;EACpDY,0BAA0B,GAAG,CAAC;EAE9BC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EACVC,OAAO,GAAG,CAAC;EACXC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,KAAK,GAAG,CAAC;EAKTC,SAAS,GAAG,CAAC;EAEdC,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,KAAK,CAACF,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IAEzD,IAAI,IAAI,CAACC,MAAM,CAACb,YAAY,KAAKc,SAAS,EAAE;MAC1C,IAAI,CAACd,YAAY,GAAG,IAAI,CAACa,MAAM,CAACb,YAAY;IAC9C;IAEA,IAAI,IAAI,CAACa,MAAM,CAACf,aAAa,KAAKgB,SAAS,EAAE;MAC3C,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACe,MAAM,CAACf,aAAa;IAChD;IAEA,IAAI,IAAI,CAACe,MAAM,CAACd,UAAU,KAAKe,SAAS,EAAE;MACxC,IAAI,CAACf,UAAU,GAAG,IAAI,CAACc,MAAM,CAACd,UAAU;IAC1C;IAEA,IAAI,IAAI,CAACc,MAAM,CAACpB,SAAS,KAAKqB,SAAS,EAAE;MACvC,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACoB,MAAM,CAACpB,SAAS;IACxC;IAEA,IAAI,IAAI,CAACoB,MAAM,CAACjB,SAAS,KAAKkB,SAAS,EAAE;MACvC,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACiB,MAAM,CAACjB,SAAS;IACxC;IAEA,IAAI,IAAI,CAACiB,MAAM,CAACE,OAAO,KAAKD,SAAS,EAAE;MACrC,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACgB,MAAM,CAACE,OAAO,GAAG,IAAI,CAACF,MAAM,CAACE,OAAO;IAC5D;IAEA,IAAI,IAAI,CAACF,MAAM,CAACG,WAAW,KAAKF,SAAS,EAAE;MACzC,IAAI,CAACb,mBAAmB,GAAG,IAAI,CAACY,MAAM,CAACG,WAAW;IACpD;EACF;EAEUC,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IAEnB,IAAI,CAACxB,SAAS,GAAGC,MAAM,CAACC,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGF,MAAM,CAACC,gBAAgB;IACxC,IAAI,CAACE,SAAS,GAAGH,MAAM,CAACC,gBAAgB;IACxC,IAAI,CAACG,aAAa,GAAGX,uBAAuB;IAC5C,IAAI,CAACY,UAAU,GAAGX,oBAAoB;IACtC,IAAI,CAACY,YAAY,GAAGX,sBAAsB;IAC1C,IAAI,CAACY,mBAAmB,GAAGX,8BAA8B;EAC3D;EAEQ4B,aAAaA,CAAA,EAAS;IAC5BC,YAAY,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9BD,YAAY,CAAC,IAAI,CAACE,YAAY,CAAC;EACjC;EAEQC,QAAQA,CAAA,EAAS;IACvB,IAAI,CAACJ,aAAa,CAAC,CAAC;IAEpB,IAAI,CAACE,WAAW,GAAGG,UAAU,CAAC,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC1B,aAAa,CAAC;EACtE;EAEQ2B,MAAMA,CAAA,EAAS;IACrB,IAAI,CAACP,aAAa,CAAC,CAAC;IAEpB,IACE,EAAE,IAAI,CAACT,SAAS,KAAK,IAAI,CAACT,YAAY,IACtC,IAAI,CAACE,0BAA0B,IAAI,IAAI,CAACD,mBAAmB,EAC3D;MACA,IAAI,CAACyB,QAAQ,CAAC,CAAC;IACjB,CAAC,MAAM;MACL,IAAI,CAACL,YAAY,GAAGE,UAAU,CAAC,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACzB,UAAU,CAAC;IACpE;EACF;EAEQ4B,gBAAgBA,CAAA,EAAG;IACzB,MAAM;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,OAAO,CAACC,wBAAwB,CAAC,CAAC;IAExD,IAAI,CAACxB,KAAK,GAAGqB,CAAC;IACd,IAAI,CAACpB,KAAK,GAAGqB,CAAC;EAChB;;EAEA;EACUG,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACL,OAAO,CAACM,YAAY,CAACH,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACI,kBAAkB,CAACJ,KAAK,CAAC;IAE9B,IAAI,CAAC9B,MAAM,GAAG8B,KAAK,CAACL,CAAC;IACrB,IAAI,CAACxB,MAAM,GAAG6B,KAAK,CAACJ,CAAC;IAErB,IAAI,CAACtB,KAAK,GAAG0B,KAAK,CAACL,CAAC;IACpB,IAAI,CAACpB,KAAK,GAAGyB,KAAK,CAACJ,CAAC;IAEpB,IAAI,CAACS,WAAW,CAACL,KAAK,CAAC;IAEvB,IAAI,CAACM,mBAAmB,CAACN,KAAK,CAAC;EACjC;EAEUO,YAAYA,CAACP,KAAmB,EAAQ;IAChD,KAAK,CAACO,YAAY,CAACP,KAAK,CAAC;IACzB,IAAI,CAACH,OAAO,CAACM,YAAY,CAACH,KAAK,CAAC;IAChC,IAAI,CAACI,kBAAkB,CAACJ,KAAK,CAAC;IAE9B,IAAI,CAAC5B,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,IAAI,CAACuB,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACxB,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,CAAC8B,WAAW,CAACL,KAAK,CAAC;EACzB;EAEUQ,WAAWA,CAACR,KAAmB,EAAQ;IAC/C,KAAK,CAACQ,WAAW,CAACR,KAAK,CAAC;IAExB,IAAI,CAACN,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACG,OAAO,CAACY,iBAAiB,CAACT,KAAK,CAACU,SAAS,CAAC;IAE/C,IAAI,CAACL,WAAW,CAACL,KAAK,CAAC;EACzB;EAEUW,eAAeA,CAACX,KAAmB,EAAQ;IACnD,KAAK,CAACW,eAAe,CAACX,KAAK,CAAC;IAC5B,IAAI,CAACH,OAAO,CAACY,iBAAiB,CAACT,KAAK,CAACU,SAAS,CAAC;IAE/C,IAAI,CAACtC,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,IAAI,CAACuB,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACxB,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,CAAC8B,WAAW,CAACL,KAAK,CAAC;EACzB;EAEUY,aAAaA,CAACZ,KAAmB,EAAQ;IACjD,IAAI,CAACI,kBAAkB,CAACJ,KAAK,CAAC;IAC9B,IAAI,CAACH,OAAO,CAACgB,KAAK,CAACb,KAAK,CAAC;IAEzB,IAAI,CAACN,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACW,WAAW,CAACL,KAAK,CAAC;IAEvB,KAAK,CAACY,aAAa,CAACZ,KAAK,CAAC;EAC5B;EAEUc,oBAAoBA,CAACd,KAAmB,EAAQ;IACxD,IAAI,CAACI,kBAAkB,CAACJ,KAAK,CAAC;IAC9B,IAAI,CAACH,OAAO,CAACgB,KAAK,CAACb,KAAK,CAAC;IAEzB,IAAI,CAACN,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACW,WAAW,CAACL,KAAK,CAAC;IAEvB,KAAK,CAACc,oBAAoB,CAACd,KAAK,CAAC;EACnC;EAEQK,WAAWA,CAACL,KAAmB,EAAQ;IAC7C,IAAI,IAAI,CAAC/B,0BAA0B,GAAG,IAAI,CAAC4B,OAAO,CAACkB,oBAAoB,EAAE;MACvE,IAAI,CAAC9C,0BAA0B,GAAG,IAAI,CAAC4B,OAAO,CAACkB,oBAAoB;IACrE;IAEA,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACzB,IAAI,CAAC,CAAC;MACX;IACF;IAEA,QAAQ,IAAI,CAAC0B,KAAK;MAChB,KAAKC,YAAK,CAACC,YAAY;QACrB,IAAInB,KAAK,CAACoB,SAAS,KAAKC,sBAAU,CAACC,IAAI,EAAE;UACvC,IAAI,CAACC,KAAK,CAAC,CAAC;QACd;QACA,IAAI,CAAClC,QAAQ,CAAC,CAAC;QACf;MACF,KAAK6B,YAAK,CAACM,KAAK;QACd,IAAIxB,KAAK,CAACoB,SAAS,KAAKC,sBAAU,CAACI,EAAE,EAAE;UACrC,IAAI,CAACjC,MAAM,CAAC,CAAC;QACf;QACA,IAAIQ,KAAK,CAACoB,SAAS,KAAKC,sBAAU,CAACC,IAAI,EAAE;UACvC,IAAI,CAACjC,QAAQ,CAAC,CAAC;QACjB;QACA;MACF;QACE;IACJ;EACF;EAEQe,kBAAkBA,CAACJ,KAAmB,EAAQ;IACpD,IAAI,IAAI,CAACiB,KAAK,KAAKC,YAAK,CAACC,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAAC/C,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACH,MAAM,GAAG8B,KAAK,CAACL,CAAC;IACrB,IAAI,CAACxB,MAAM,GAAG6B,KAAK,CAACJ,CAAC;EACvB;EAEQoB,UAAUA,CAAA,EAAY;IAC5B,MAAMU,EAAE,GAAG,IAAI,CAACpD,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;IAElD,IACE,IAAI,CAACZ,SAAS,KAAKC,MAAM,CAACC,gBAAgB,IAC1CiE,IAAI,CAACC,GAAG,CAACF,EAAE,CAAC,GAAG,IAAI,CAAClE,SAAS,EAC7B;MACA,OAAO,IAAI;IACb;IAEA,MAAMqE,EAAE,GAAG,IAAI,CAACtD,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;IAClD,IACE,IAAI,CAACV,SAAS,KAAKF,MAAM,CAACC,gBAAgB,IAC1CiE,IAAI,CAACC,GAAG,CAACC,EAAE,CAAC,GAAG,IAAI,CAAClE,SAAS,EAC7B;MACA,OAAO,IAAI;IACb;IAEA,MAAMmE,MAAM,GAAGD,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE;IAEhC,OACE,IAAI,CAAC9D,SAAS,KAAKH,MAAM,CAACC,gBAAgB,IAAIoE,MAAM,GAAG,IAAI,CAAClE,SAAS;EAEzE;EAEO6B,QAAQA,CAAA,EAAS;IACtB,KAAK,CAACA,QAAQ,CAAC,CAAC;IAEhB,IAAI,CAACsC,GAAG,CAAC,CAAC;EACZ;EAEUC,QAAQA,CAAA,EAAS;IACzB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAAChD,aAAa,CAAC,CAAC;EACtB;EAEUgD,aAAaA,CAAA,EAAS;IAC9B,IAAI,CAAChD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACT,SAAS,GAAG,CAAC;IAClB,IAAI,CAACP,0BAA0B,GAAG,CAAC;EACrC;AACF;AAACiE,OAAA,CAAAjF,OAAA,GAAAK,iBAAA", "ignoreList": []}