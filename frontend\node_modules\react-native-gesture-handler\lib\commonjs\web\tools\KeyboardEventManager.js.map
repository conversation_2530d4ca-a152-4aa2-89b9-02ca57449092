{"version": 3, "names": ["_interfaces", "require", "_EventManager", "_interopRequireDefault", "_PointerType", "e", "__esModule", "default", "KeyboardEventManager", "EventManager", "activationKeys", "cancelationKeys", "isPressed", "registeredStaticListeners", "instances", "Set", "keyUpStatic<PERSON><PERSON>back", "event", "indexOf", "key", "for<PERSON>ach", "item", "onKeyUp", "keyDownCallback", "dispatchEvent", "EventTypes", "CANCEL", "DOWN", "UP", "eventType", "target", "HTMLElement", "adaptedEvent", "mapEvent", "onPointerUp", "onPointerDown", "onPointerCancel", "registerListeners", "view", "addEventListener", "add", "document", "capture", "unregisterListeners", "removeEventListener", "delete", "size", "viewRect", "getBoundingClientRect", "viewportPosition", "x", "width", "y", "height", "relativePosition", "offsetX", "offsetY", "pointerId", "pointerType", "PointerType", "KEY", "time", "timeStamp", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/KeyboardEventManager.ts"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAAgD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEjC,MAAMG,oBAAoB,SAASC,qBAAY,CAAc;EAC1E,OAAeC,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC;EAC9C,OAAeC,eAAe,GAAG,CAAC,KAAK,CAAC;EAChCC,SAAS,GAAG,KAAK;EACzB,OAAeC,yBAAyB,GAAG,KAAK;EAChD,OAAeC,SAAS,GAA8B,IAAIC,GAAG,CAAC,CAAC;EAE/D,OAAeC,mBAAmB,GAAIC,KAAoB,IAAW;IACnE;IACA;IACA;;IAEA,IAAI,IAAI,CAACP,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAACL,SAAS,CAACM,OAAO,CAAEC,IAAI,IAAK;MAC/BA,IAAI,CAACC,OAAO,CAACL,KAAK,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EAEOM,eAAe,GAAIN,KAAoB,IAAW;IACxD,IACET,oBAAoB,CAACG,eAAe,CAACO,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAC9D,IAAI,CAACP,SAAS,EACd;MACA,IAAI,CAACY,aAAa,CAACP,KAAK,EAAEQ,sBAAU,CAACC,MAAM,CAAC;MAC5C;IACF;IAEA,IAAIlB,oBAAoB,CAACE,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjE;IACF;IAEA,IAAI,CAACK,aAAa,CAACP,KAAK,EAAEQ,sBAAU,CAACE,IAAI,CAAC;EAC5C,CAAC;EAEOL,OAAO,GAAIL,KAAoB,IAAW;IAChD,IACET,oBAAoB,CAACE,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAC7D,CAAC,IAAI,CAACP,SAAS,EACf;MACA;IACF;IAEA,IAAI,CAACY,aAAa,CAACP,KAAK,EAAEQ,sBAAU,CAACG,EAAE,CAAC;EAC1C,CAAC;EAEOJ,aAAaA,CAACP,KAAoB,EAAEY,SAAqB,EAAE;IACjE,IAAI,EAAEZ,KAAK,CAACa,MAAM,YAAYC,WAAW,CAAC,EAAE;MAC1C;IACF;IAEA,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAAChB,KAAK,EAAEY,SAAS,CAAC;IAEpD,QAAQA,SAAS;MACf,KAAKJ,sBAAU,CAACG,EAAE;QAChB,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACsB,WAAW,CAACF,YAAY,CAAC;QAC9B;MACF,KAAKP,sBAAU,CAACE,IAAI;QAClB,IAAI,CAACf,SAAS,GAAG,IAAI;QACrB,IAAI,CAACuB,aAAa,CAACH,YAAY,CAAC;QAChC;MACF,KAAKP,sBAAU,CAACC,MAAM;QACpB,IAAI,CAACd,SAAS,GAAG,KAAK;QACtB,IAAI,CAACwB,eAAe,CAACJ,YAAY,CAAC;QAClC;IACJ;EACF;EAEOK,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAChB,eAAe,CAAC;IAE3Df,oBAAoB,CAACM,SAAS,CAAC0B,GAAG,CAAC,IAAI,CAAC;IAExC,IAAI,CAAChC,oBAAoB,CAACK,yBAAyB,EAAE;MACnDL,oBAAoB,CAACK,yBAAyB,GAAG,IAAI;MACrD4B,QAAQ,CAACF,gBAAgB,CACvB,OAAO,EACP/B,oBAAoB,CAACQ,mBAAmB,EACxC;QAAE0B,OAAO,EAAE;MAAK,CAClB,CAAC;IACH;EACF;EAEOC,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACL,IAAI,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrB,eAAe,CAAC;IAE9Df,oBAAoB,CAACM,SAAS,CAAC+B,MAAM,CAAC,IAAI,CAAC;IAE3C,IAAIrC,oBAAoB,CAACM,SAAS,CAACgC,IAAI,KAAK,CAAC,EAAE;MAC7CL,QAAQ,CAACG,mBAAmB,CAC1B,OAAO,EACPpC,oBAAoB,CAACQ,mBAAmB,EACxC;QAAE0B,OAAO,EAAE;MAAK,CAClB,CAAC;MACDlC,oBAAoB,CAACK,yBAAyB,GAAG,KAAK;IACxD;EACF;EAEUoB,QAAQA,CAChBhB,KAAoB,EACpBY,SAAqB,EACP;IACd,MAAMkB,QAAQ,GAAI9B,KAAK,CAACa,MAAM,CAAiBkB,qBAAqB,CAAC,CAAC;IAEtE,MAAMC,gBAAgB,GAAG;MACvBC,CAAC,EAAEH,QAAQ,EAAEG,CAAC,GAAGH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACpCC,CAAC,EAAEL,QAAQ,EAAEK,CAAC,GAAGL,QAAQ,EAAEM,MAAM,GAAG;IACtC,CAAC;IAED,MAAMC,gBAAgB,GAAG;MACvBJ,CAAC,EAAEH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACtBC,CAAC,EAAEL,QAAQ,EAAEM,MAAM,GAAG;IACxB,CAAC;IAED,OAAO;MACLH,CAAC,EAAED,gBAAgB,CAACC,CAAC;MACrBE,CAAC,EAAEH,gBAAgB,CAACG,CAAC;MACrBG,OAAO,EAAED,gBAAgB,CAACJ,CAAC;MAC3BM,OAAO,EAAEF,gBAAgB,CAACF,CAAC;MAC3BK,SAAS,EAAE,CAAC;MACZ5B,SAAS,EAAEA,SAAS;MACpB6B,WAAW,EAAEC,wBAAW,CAACC,GAAG;MAC5BC,IAAI,EAAE5C,KAAK,CAAC6C;IACd,CAAC;EACH;AACF;AAACC,OAAA,CAAAxD,OAAA,GAAAC,oBAAA", "ignoreList": []}