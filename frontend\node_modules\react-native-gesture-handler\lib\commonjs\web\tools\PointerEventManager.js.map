{"version": 3, "names": ["_EventManager", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_interfaces", "_utils", "_PointerType", "e", "__esModule", "default", "POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "EventManager", "trackedPointers", "mouseButtonsMapper", "Map", "constructor", "view", "set", "MouseB<PERSON>on", "LEFT", "MIDDLE", "RIGHT", "BUTTON_4", "BUTTON_5", "lastPosition", "x", "Infinity", "y", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "isPointerInBounds", "clientX", "clientY", "adaptedEvent", "mapEvent", "EventTypes", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "pointerCancelCallback", "CANCEL", "onPointerCancel", "clear", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "onPointerMoveOver", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onPointerMoveOut", "lostPointerCaptureCallback", "registerListeners", "addEventListener", "unregisterListeners", "removeEventListener", "rect", "getBoundingClientRect", "scaleX", "scaleY", "calculateViewScale", "offsetX", "left", "offsetY", "top", "pointerType", "PointerTypeMapping", "get", "PointerType", "OTHER", "button", "time", "timeStamp", "stylusData", "tryExtractStylusData", "resetManager", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/PointerEventManager.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAMA,IAAAI,YAAA,GAAAJ,OAAA;AAAgD,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhD,MAAMG,4BAA4B,GAAG,IAAIC,GAAG,CAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAE1D,MAAMC,mBAAmB,SAASC,qBAAY,CAAc;EACjEC,eAAe,GAAG,IAAIH,GAAG,CAAS,CAAC;EAC1BI,kBAAkB,GAAG,IAAIC,GAAG,CAAsB,CAAC;EAGpEC,WAAWA,CAACC,IAAiB,EAAE;IAC7B,KAAK,CAACA,IAAI,CAAC;IAEX,IAAI,CAACH,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEC,iCAAW,CAACC,IAAI,CAAC;IAChD,IAAI,CAACN,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEC,iCAAW,CAACE,MAAM,CAAC;IAClD,IAAI,CAACP,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEC,iCAAW,CAACG,KAAK,CAAC;IACjD,IAAI,CAACR,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEC,iCAAW,CAACI,QAAQ,CAAC;IACpD,IAAI,CAACT,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEC,iCAAW,CAACK,QAAQ,CAAC;IAEpD,IAAI,CAACC,YAAY,GAAG;MAClBC,CAAC,EAAE,CAACC,QAAQ;MACZC,CAAC,EAAE,CAACD;IACN,CAAC;EACH;EAEQE,mBAAmB,GAAIC,KAAmB,IAAK;IACrD,IAAI,CAAC,IAAAC,wBAAiB,EAAC,IAAI,CAACd,IAAI,EAAE;MAAES,CAAC,EAAEI,KAAK,CAACE,OAAO;MAAEJ,CAAC,EAAEE,KAAK,CAACG;IAAQ,CAAC,CAAC,EAAE;MACzE;IACF;IAEA,MAAMC,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACC,IAAI,CAAC;IACxE,MAAMC,MAAM,GAAGR,KAAK,CAACQ,MAAqB;IAE1C,IAAI,CAAC7B,4BAA4B,CAAC8B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EAAE;MACrDF,MAAM,CAACG,iBAAiB,CAACP,YAAY,CAACQ,SAAS,CAAC;IAClD;IAEA,IAAI,CAACC,cAAc,CAACT,YAAY,CAACQ,SAAS,CAAC;IAC3C,IAAI,CAAC7B,eAAe,CAAC+B,GAAG,CAACV,YAAY,CAACQ,SAAS,CAAC;IAEhD,IAAI,EAAE,IAAI,CAACG,qBAAqB,GAAG,CAAC,EAAE;MACpCX,YAAY,CAACY,SAAS,GAAGV,sBAAU,CAACW,uBAAuB;MAC3D,IAAI,CAACC,YAAY,CAACd,YAAY,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACe,aAAa,CAACf,YAAY,CAAC;IAClC;EACF,CAAC;EAEOgB,iBAAiB,GAAIpB,KAAmB,IAAK;IACnD;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACe,qBAAqB,KAAK,CAAC,EAAE;MACpC;IACF;IAEA,MAAMX,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACe,EAAE,CAAC;IACtE,MAAMb,MAAM,GAAGR,KAAK,CAACQ,MAAqB;IAE1C,IAAI,CAAC7B,4BAA4B,CAAC8B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EAAE;MACrDF,MAAM,CAACc,qBAAqB,CAAClB,YAAY,CAACQ,SAAS,CAAC;IACtD;IAEA,IAAI,CAACW,iBAAiB,CAACnB,YAAY,CAACQ,SAAS,CAAC;IAC9C,IAAI,CAAC7B,eAAe,CAACyC,MAAM,CAACpB,YAAY,CAACQ,SAAS,CAAC;IAEnD,IAAI,EAAE,IAAI,CAACG,qBAAqB,GAAG,CAAC,EAAE;MACpCX,YAAY,CAACY,SAAS,GAAGV,sBAAU,CAACmB,qBAAqB;MACzD,IAAI,CAACC,eAAe,CAACtB,YAAY,CAAC;IACpC,CAAC,MAAM;MACL,IAAI,CAACuB,WAAW,CAACvB,YAAY,CAAC;IAChC;EACF,CAAC;EAEOwB,mBAAmB,GAAI5B,KAAmB,IAAK;IACrD,MAAMI,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACuB,IAAI,CAAC;IACxE,MAAMrB,MAAM,GAAGR,KAAK,CAACQ,MAAqB;;IAE1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IACE,CAACA,MAAM,EAAEsB,iBAAiB,CAAC9B,KAAK,CAACY,SAAS,CAAC,IAC3C,CAACjC,4BAA4B,CAAC8B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EACjD;MACAF,MAAM,CAACG,iBAAiB,CAACX,KAAK,CAACY,SAAS,CAAC;IAC3C;IAEA,MAAMmB,QAAiB,GAAG,IAAA9B,wBAAiB,EAAC,IAAI,CAACd,IAAI,EAAE;MACrDS,CAAC,EAAEQ,YAAY,CAACR,CAAC;MACjBE,CAAC,EAAEM,YAAY,CAACN;IAClB,CAAC,CAAC;IAEF,MAAMkC,YAAoB,GAAG,IAAI,CAACC,gBAAgB,CAACC,OAAO,CACxD9B,YAAY,CAACQ,SACf,CAAC;IAED,IAAImB,QAAQ,EAAE;MACZ,IAAIC,YAAY,GAAG,CAAC,EAAE;QACpB5B,YAAY,CAACY,SAAS,GAAGV,sBAAU,CAAC6B,KAAK;QACzC,IAAI,CAACC,cAAc,CAAChC,YAAY,CAAC;QACjC,IAAI,CAACS,cAAc,CAACT,YAAY,CAACQ,SAAS,CAAC;MAC7C,CAAC,MAAM;QACL,IAAI,CAACyB,aAAa,CAACjC,YAAY,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI4B,YAAY,IAAI,CAAC,EAAE;QACrB5B,YAAY,CAACY,SAAS,GAAGV,sBAAU,CAACgC,KAAK;QACzC,IAAI,CAACC,cAAc,CAACnC,YAAY,CAAC;QACjC,IAAI,CAACmB,iBAAiB,CAACnB,YAAY,CAACQ,SAAS,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAAC4B,oBAAoB,CAACpC,YAAY,CAAC;MACzC;IACF;IAEA,IAAI,CAACT,YAAY,CAACC,CAAC,GAAGI,KAAK,CAACJ,CAAC;IAC7B,IAAI,CAACD,YAAY,CAACG,CAAC,GAAGE,KAAK,CAACF,CAAC;EAC/B,CAAC;EAEO2C,qBAAqB,GAAIzC,KAAmB,IAAK;IACvD,MAAMI,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACoC,MAAM,CAAC;IAE1E,IAAI,CAACC,eAAe,CAACvC,YAAY,CAAC;IAClC,IAAI,CAACmB,iBAAiB,CAACnB,YAAY,CAACQ,SAAS,CAAC;IAC9C,IAAI,CAACG,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAAChC,eAAe,CAAC6D,KAAK,CAAC,CAAC;EAC9B,CAAC;EAEOC,oBAAoB,GAAI7C,KAAmB,IAAK;IACtD,MAAMI,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAAC6B,KAAK,CAAC;IAEzE,IAAI,CAACW,iBAAiB,CAAC1C,YAAY,CAAC;EACtC,CAAC;EAEO2C,oBAAoB,GAAI/C,KAAmB,IAAK;IACtD,MAAMI,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACgC,KAAK,CAAC;IAEzE,IAAI,CAACU,gBAAgB,CAAC5C,YAAY,CAAC;EACrC,CAAC;EAEO6C,0BAA0B,GAAIjD,KAAmB,IAAK;IAC5D,MAAMI,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEM,sBAAU,CAACoC,MAAM,CAAC;IAE1E,IAAI,IAAI,CAAC3D,eAAe,CAAC0B,GAAG,CAACL,YAAY,CAACQ,SAAS,CAAC,EAAE;MACpD;MACA;MACA,IAAI,CAAC+B,eAAe,CAACvC,YAAY,CAAC;MAElC,IAAI,CAACW,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAAChC,eAAe,CAAC6D,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC;EAEMM,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAAC/D,IAAI,CAACgE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACpD,mBAAmB,CAAC;IACnE,IAAI,CAACZ,IAAI,CAACgE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC/B,iBAAiB,CAAC;IAC/D,IAAI,CAACjC,IAAI,CAACgE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACvB,mBAAmB,CAAC;IACnE,IAAI,CAACzC,IAAI,CAACgE,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACV,qBAAqB,CAAC;;IAEvE;IACA;IACA;IACA;IACA,IAAI,CAACtD,IAAI,CAACgE,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACN,oBAAoB,CAAC;IACrE,IAAI,CAAC1D,IAAI,CAACgE,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACJ,oBAAoB,CAAC;IACrE,IAAI,CAAC5D,IAAI,CAACgE,gBAAgB,CACxB,oBAAoB,EACpB,IAAI,CAACF,0BACP,CAAC;EACH;EAEOG,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACjE,IAAI,CAACkE,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACtD,mBAAmB,CAAC;IACtE,IAAI,CAACZ,IAAI,CAACkE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAAC;IAClE,IAAI,CAACjC,IAAI,CAACkE,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACzB,mBAAmB,CAAC;IACtE,IAAI,CAACzC,IAAI,CAACkE,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACZ,qBAAqB,CAAC;IAC1E,IAAI,CAACtD,IAAI,CAACkE,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACR,oBAAoB,CAAC;IACxE,IAAI,CAAC1D,IAAI,CAACkE,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACN,oBAAoB,CAAC;IACxE,IAAI,CAAC5D,IAAI,CAACkE,mBAAmB,CAC3B,oBAAoB,EACpB,IAAI,CAACJ,0BACP,CAAC;EACH;EAEU5C,QAAQA,CAACL,KAAmB,EAAEgB,SAAqB,EAAgB;IAC3E,MAAMsC,IAAI,GAAG,IAAI,CAACnE,IAAI,CAACoE,qBAAqB,CAAC,CAAC;IAC9C,MAAM;MAAEC,MAAM;MAAEC;IAAO,CAAC,GAAG,IAAAC,yBAAkB,EAAC,IAAI,CAACvE,IAAI,CAAC;IAExD,OAAO;MACLS,CAAC,EAAEI,KAAK,CAACE,OAAO;MAChBJ,CAAC,EAAEE,KAAK,CAACG,OAAO;MAChBwD,OAAO,EAAE,CAAC3D,KAAK,CAACE,OAAO,GAAGoD,IAAI,CAACM,IAAI,IAAIJ,MAAM;MAC7CK,OAAO,EAAE,CAAC7D,KAAK,CAACG,OAAO,GAAGmD,IAAI,CAACQ,GAAG,IAAIL,MAAM;MAC5C7C,SAAS,EAAEZ,KAAK,CAACY,SAAS;MAC1BI,SAAS,EAAEA,SAAS;MACpB+C,WAAW,EACTC,yBAAkB,CAACC,GAAG,CAACjE,KAAK,CAAC+D,WAAW,CAAC,IAAIG,wBAAW,CAACC,KAAK;MAChEC,MAAM,EAAE,IAAI,CAACpF,kBAAkB,CAACiF,GAAG,CAACjE,KAAK,CAACoE,MAAM,CAAC;MACjDC,IAAI,EAAErE,KAAK,CAACsE,SAAS;MACrBC,UAAU,EAAE,IAAAC,2BAAoB,EAACxE,KAAK;IACxC,CAAC;EACH;EAEOyE,YAAYA,CAAA,EAAS;IAC1B,KAAK,CAACA,YAAY,CAAC,CAAC;IACpB,IAAI,CAAC1F,eAAe,CAAC6D,KAAK,CAAC,CAAC;EAC9B;AACF;AAAC8B,OAAA,CAAAhG,OAAA,GAAAG,mBAAA", "ignoreList": []}