{"version": 3, "names": ["_CircularBuffer", "_interopRequireDefault", "require", "_LeastSquareSolver", "e", "__esModule", "default", "VelocityTracker", "assumePointerMoveStoppedMilliseconds", "historySize", "horizonMilliseconds", "minSampleSize", "constructor", "samples", "Circular<PERSON><PERSON>er", "add", "event", "push", "getVelocityEstimate", "x", "y", "w", "time", "sampleCount", "index", "size", "newestSample", "get", "previousSample", "sample", "age", "delta", "Math", "abs", "xSolver", "LeastSquareSolver", "xFit", "solve", "ySolver", "yFit", "xVelocity", "coefficients", "yVelocity", "velocity", "estimate", "reset", "clear", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/VelocityTracker.ts"], "mappings": ";;;;;;AACA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAoD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAErC,MAAMG,eAAe,CAAC;EAC3BC,oCAAoC,GAAG,EAAE;EACzCC,WAAW,GAAG,EAAE;EAChBC,mBAAmB,GAAG,GAAG;EACzBC,aAAa,GAAG,CAAC;EAIzBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAIC,uBAAc,CAAe,IAAI,CAACL,WAAW,CAAC;EACnE;EAEOM,GAAGA,CAACC,KAAmB,EAAQ;IACpC,IAAI,CAACH,OAAO,CAACI,IAAI,CAACD,KAAK,CAAC;EAC1B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACQE,mBAAmBA,CAAA,EAA4B;IACrD,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,IAAI,GAAG,EAAE;IAEf,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,IAAI,GAAG,CAAC;IACjC,MAAMC,YAAY,GAAG,IAAI,CAACb,OAAO,CAACc,GAAG,CAACH,KAAK,CAAC;IAC5C,IAAI,CAACE,YAAY,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAIE,cAAc,GAAGF,YAAY;;IAEjC;IACA;IACA,OAAOH,WAAW,GAAG,IAAI,CAACV,OAAO,CAACY,IAAI,EAAE;MACtC,MAAMI,MAAM,GAAG,IAAI,CAAChB,OAAO,CAACc,GAAG,CAACH,KAAK,CAAC;MAEtC,MAAMM,GAAG,GAAGJ,YAAY,CAACJ,IAAI,GAAGO,MAAM,CAACP,IAAI;MAC3C,MAAMS,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAACP,IAAI,GAAGM,cAAc,CAACN,IAAI,CAAC;MACzDM,cAAc,GAAGC,MAAM;MAEvB,IACEC,GAAG,GAAG,IAAI,CAACpB,mBAAmB,IAC9BqB,KAAK,GAAG,IAAI,CAACvB,oCAAoC,EACjD;QACA;MACF;MAEAW,CAAC,CAACF,IAAI,CAACY,MAAM,CAACV,CAAC,CAAC;MAChBC,CAAC,CAACH,IAAI,CAACY,MAAM,CAACT,CAAC,CAAC;MAChBC,CAAC,CAACJ,IAAI,CAAC,CAAC,CAAC;MACTK,IAAI,CAACL,IAAI,CAAC,CAACa,GAAG,CAAC;MAEfP,WAAW,EAAE;MACbC,KAAK,EAAE;IACT;IAEA,IAAID,WAAW,IAAI,IAAI,CAACZ,aAAa,EAAE;MACrC,MAAMuB,OAAO,GAAG,IAAIC,0BAAiB,CAACb,IAAI,EAAEH,CAAC,EAAEE,CAAC,CAAC;MACjD,MAAMe,IAAI,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC;MAE7B,IAAID,IAAI,KAAK,IAAI,EAAE;QACjB,MAAME,OAAO,GAAG,IAAIH,0BAAiB,CAACb,IAAI,EAAEF,CAAC,EAAEC,CAAC,CAAC;QACjD,MAAMkB,IAAI,GAAGD,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC;QAE7B,IAAIE,IAAI,KAAK,IAAI,EAAE;UACjB,MAAMC,SAAS,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;UAC7C,MAAMC,SAAS,GAAGH,IAAI,CAACE,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;UAE7C,OAAO,CAACD,SAAS,EAAEE,SAAS,CAAC;QAC/B;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA,IAAWC,QAAQA,CAAA,EAAqB;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAAC1B,mBAAmB,CAAC,CAAC;IAC3C,IAAI0B,QAAQ,KAAK,IAAI,EAAE;MACrB,OAAOA,QAAQ;IACjB;IACA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACf;EAEOC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAAChC,OAAO,CAACiC,KAAK,CAAC,CAAC;EACtB;AACF;AAACC,OAAA,CAAAzC,OAAA,GAAAC,eAAA", "ignoreList": []}