{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_State", "_constants", "_DiscreteGestureHandler", "_utils", "e", "__esModule", "default", "PressGestureHandler", "DiscreteGestureHandler", "initialEvent", "name", "minDurationMs", "isnan", "config", "maxDist", "NativeGestureClass", "Hammer", "Press", "shouldDelayTouches", "simulateCancelEvent", "inputData", "hasGestureFailed", "cancelEvent", "updateHasCustomActivationCriteria", "shouldCancelWhenOutside", "maxDistSq", "isValidNumber", "getState", "type", "INPUT_START", "State", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "CANCELLED", "getConfig", "hasCustomActivationCriteria", "getHammerConfig", "time", "onGestureActivated", "ev", "onGestureStart", "shouldDelayTouchForEvent", "pointerType", "isGestureRunning", "clearTimeout", "visualFeedbackTimer", "fireAfterInterval", "sendGestureStartedEvent", "CONTENT_TOUCHES_DELAY", "sendEvent", "eventType", "<PERSON><PERSON><PERSON><PERSON>", "forceInvalidate", "event", "onRawEvent", "isFinal", "timeout", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "onGestureEnded", "updateGestureConfig", "shouldActivateOnStart", "disallowInterruption", "Number", "NaN", "minPointers", "maxPointers", "props", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/PressGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAKA,IAAAG,uBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAAkE,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElE,MAAMG,mBAAmB,SAASC,+BAAsB,CAAC;EAE/CC,YAAY,GAA0B,IAAI;EAClD,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,aAAaA,CAAA,EAAG;IAClB;IACA,OAAO,IAAAC,YAAK,EAAC,IAAI,CAACC,MAAM,CAACF,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACE,MAAM,CAACF,aAAa;EACzE;EAEA,IAAIG,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAAF,YAAK,EAAC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;EAC7D;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,KAAK;EACrB;EAEAC,kBAAkB,GAAG,IAAI;EAEzBC,mBAAmBA,CAACC,SAAyB,EAAE;IAC7C;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,CAACF,SAAS,CAAC;EAC7B;EAEAG,iCAAiCA,CAAC;IAChCC,uBAAuB;IACvBC;EAC6C,CAAC,EAAE;IAChD,OAAOD,uBAAuB,IAAI,CAAC,IAAAE,oBAAa,EAACD,SAAS,CAAC;EAC7D;EAEAE,QAAQA,CAACC,IAAmC,EAAS;IACnD,OAAO;MACL,CAACZ,iBAAM,CAACa,WAAW,GAAGC,YAAK,CAACC,KAAK;MACjC,CAACf,iBAAM,CAACgB,UAAU,GAAGF,YAAK,CAACG,MAAM;MACjC,CAACjB,iBAAM,CAACkB,SAAS,GAAGJ,YAAK,CAACK,GAAG;MAC7B,CAACnB,iBAAM,CAACoB,YAAY,GAAGN,YAAK,CAACO;IAC/B,CAAC,CAACT,IAAI,CAAC;EACT;EAEAU,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLf,uBAAuB,EAAE,IAAI;QAC7BC,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACZ,MAAM;EACpB;EAEA2B,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1B;MACAC,IAAI,EAAE,IAAI,CAAC9B;IACb,CAAC;EACH;EAEA+B,kBAAkBA,CAACC,EAAkB,EAAE;IACrC,IAAI,CAACC,cAAc,CAACD,EAAE,CAAC;EACzB;EAEAE,wBAAwBA,CAAC;IAAEC;EAA4B,CAAC,EAAE;IACxD;IACA,OAAO,IAAI,CAAC5B,kBAAkB,IAAI4B,WAAW,KAAK,OAAO;EAC3D;EAEAF,cAAcA,CAACD,EAAkB,EAAE;IACjC,IAAI,CAACI,gBAAgB,GAAG,IAAI;IAC5BC,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACxC,YAAY,GAAGkC,EAAE;IACtB,IAAI,CAACM,mBAAmB,GAAG,IAAAC,wBAAiB,EAC1C,MAAM;MACJ,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAAC1C,YAA8B,CAAC;MACjE,IAAI,CAACA,YAAY,GAAG,IAAI;IAC1B,CAAC,EACD,IAAI,CAACoC,wBAAwB,CAACF,EAAE,CAAC,IAAIS,gCACvC,CAAC;EACH;EAEAD,uBAAuBA,CAACR,EAAkB,EAAE;IAC1CK,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACI,SAAS,CAAC;MACb,GAAGV,EAAE;MACLW,SAAS,EAAEtC,iBAAM,CAACgB,UAAU;MAC5BuB,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAACC,KAAqB,EAAE;IACrC,KAAK,CAACD,eAAe,CAACC,KAAK,CAAC;IAC5BT,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACxC,YAAY,GAAG,IAAI;EAC1B;EAEAiD,UAAUA,CAACf,EAAkB,EAAE;IAC7B,KAAK,CAACe,UAAU,CAACf,EAAE,CAAC;IACpB,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAIJ,EAAE,CAACgB,OAAO,EAAE;QACd,IAAIC,OAAO;QACX,IAAI,IAAI,CAACX,mBAAmB,EAAE;UAC5B;UACA;UACA;UACAW,OAAO,GAAGC,8CAAmC;UAC7C,IAAI,CAACV,uBAAuB,CAAC,IAAI,CAAC1C,YAA8B,CAAC;UACjE,IAAI,CAACA,YAAY,GAAG,IAAI;QAC1B;QACA,IAAAyC,wBAAiB,EAAC,MAAM;UACtB,IAAI,CAACG,SAAS,CAAC;YACb,GAAGV,EAAE;YACLW,SAAS,EAAEtC,iBAAM,CAACkB,SAAS;YAC3ByB,OAAO,EAAE;UACX,CAAC,CAAC;UACF;UACA,IAAI,CAACG,cAAc,CAAC,CAAC;QACvB,CAAC,EAAEF,OAAO,CAAC;MACb,CAAC,MAAM;QACL,IAAI,CAACP,SAAS,CAAC;UACb,GAAGV,EAAE;UACLW,SAAS,EAAEtC,iBAAM,CAACgB,UAAU;UAC5B2B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF;EAEAI,mBAAmBA,CAAC;IAClBC,qBAAqB,GAAG,KAAK;IAC7BC,oBAAoB,GAAG,KAAK;IAC5BzC,uBAAuB,GAAG,IAAI;IAC9Bb,aAAa,GAAGuD,MAAM,CAACC,GAAG;IAC1BrD,OAAO,GAAGoD,MAAM,CAACC,GAAG;IACpBC,WAAW,GAAG,CAAC;IACfC,WAAW,GAAG,CAAC;IACf,GAAGC;EACL,CAAC,EAAE;IACD,OAAO,KAAK,CAACP,mBAAmB,CAAC;MAC/BC,qBAAqB;MACrBC,oBAAoB;MACpBzC,uBAAuB;MACvBb,aAAa;MACbG,OAAO;MACPsD,WAAW;MACXC,WAAW;MACX,GAAGC;IACL,CAAC,CAAC;EACJ;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAlE,OAAA,GACcC,mBAAmB", "ignoreList": []}