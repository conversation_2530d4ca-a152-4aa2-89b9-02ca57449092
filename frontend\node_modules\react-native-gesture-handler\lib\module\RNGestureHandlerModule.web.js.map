{"version": 3, "names": ["React", "isNewWebImplementationEnabled", "Gestures", "HammerGestures", "InteractionManager", "NodeManager", "HammerNodeManager", "GestureHandlerWebDelegate", "shouldPreventDrop", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "instance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "Element", "Component", "handler", "constructor", "name", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "sourceRoot": "../../src", "sources": ["RNGestureHandlerModule.web.ts"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,SAASC,6BAA6B,QAAQ,8BAA8B;AAC5E,SAASC,QAAQ,EAAEC,cAAc,QAAQ,gBAAgB;AAEzD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAO,KAAKC,iBAAiB,MAAM,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,uCAAuC;;AAEjF;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,KAAK;AAE7B,eAAe;EACbC,oBAAoBA,CAACC,GAAW,EAAEC,oBAA6B,EAAE;IAC/DC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEH,GAAG,EAAEC,oBAAoB,CAAC;EACnE,CAAC;EACDG,sBAAsBA,CAAA,EAAG;IACvBF,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;EAC1C,CAAC;EACDE,oBAAoBA,CAClBC,WAAkC,EAClCC,UAAkB,EAClBC,MAAS,EACT;IACA,IAAIjB,6BAA6B,CAAC,CAAC,EAAE;MACnC,IAAI,EAAEe,WAAW,IAAId,QAAQ,CAAC,EAAE;QAC9B,MAAM,IAAIiB,KAAK,CACb,iCAAiCH,WAAW,2BAC9C,CAAC;MACH;MAEA,MAAMI,YAAY,GAAGlB,QAAQ,CAACc,WAAW,CAAC;MAC1CX,WAAW,CAACU,oBAAoB,CAC9BE,UAAU,EACV,IAAIG,YAAY,CAAC,IAAIb,yBAAyB,CAAC,CAAC,CAClD,CAAC;MACDH,kBAAkB,CAACiB,QAAQ,CAACC,qBAAqB,CAC/CjB,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC,EAClCC,MACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI,EAAEF,WAAW,IAAIb,cAAc,CAAC,EAAE;QACpC,MAAM,IAAIgB,KAAK,CACb,iCAAiCH,WAAW,2BAC9C,CAAC;MACH;;MAEA;MACA;MACA,MAAMI,YAAY,GAAGjB,cAAc,CAACa,WAAW,CAAC;MAChD;MACAV,iBAAiB,CAACS,oBAAoB,CAACE,UAAU,EAAE,IAAIG,YAAY,CAAC,CAAC,CAAC;IACxE;IAEA,IAAI,CAACI,oBAAoB,CAACP,UAAU,EAAEC,MAA2B,CAAC;EACpE,CAAC;EACDO,oBAAoBA,CAClBR,UAAkB;EAClB;EACAS,OAAY,EACZC,WAAuB,EACvBC,QAAkC,EAClC;IACA,IAAI,EAAEF,OAAO,YAAYG,OAAO,IAAIH,OAAO,YAAY1B,KAAK,CAAC8B,SAAS,CAAC,EAAE;MACvEtB,iBAAiB,GAAG,IAAI;MAExB,MAAMuB,OAAO,GAAG9B,6BAA6B,CAAC,CAAC,GAC3CI,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC,GAClCX,iBAAiB,CAACiB,UAAU,CAACN,UAAU,CAAC;MAE5C,MAAMD,WAAW,GAAGe,OAAO,CAACC,WAAW,CAACC,IAAI;MAE5C,MAAM,IAAId,KAAK,CACb,GAAGH,WAAW,aAAaC,UAAU,iDACvC,CAAC;IACH;IAEA,IAAIhB,6BAA6B,CAAC,CAAC,EAAE;MACnC;MACAI,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC,CAACiB,IAAI,CAACR,OAAO,EAAEE,QAAQ,CAAC;IAC5D,CAAC,MAAM;MACL;MACAtB,iBAAiB,CAACiB,UAAU,CAACN,UAAU,CAAC,CAACkB,OAAO,CAACT,OAAO,EAAEE,QAAQ,CAAC;IACrE;EACF,CAAC;EACDJ,oBAAoBA,CAACP,UAAkB,EAAEmB,SAAiB,EAAE;IAC1D,IAAInC,6BAA6B,CAAC,CAAC,EAAE;MACnCI,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC,CAACoB,mBAAmB,CAACD,SAAS,CAAC;MAEjEhC,kBAAkB,CAACiB,QAAQ,CAACC,qBAAqB,CAC/CjB,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC,EAClCmB,SACF,CAAC;IACH,CAAC,MAAM;MACL9B,iBAAiB,CAACiB,UAAU,CAACN,UAAU,CAAC,CAACoB,mBAAmB,CAACD,SAAS,CAAC;IACzE;EACF,CAAC;EACDE,qBAAqBA,CAACrB,UAAkB,EAAE;IACxC,IAAIhB,6BAA6B,CAAC,CAAC,EAAE;MACnC,OAAOI,WAAW,CAACkB,UAAU,CAACN,UAAU,CAAC;IAC3C,CAAC,MAAM;MACL,OAAOX,iBAAiB,CAACiB,UAAU,CAACN,UAAU,CAAC;IACjD;EACF,CAAC;EACDsB,kBAAkBA,CAACtB,UAAkB,EAAE;IACrC,IAAIT,iBAAiB,EAAE;MACrB;IACF;IAEA,IAAIP,6BAA6B,CAAC,CAAC,EAAE;MACnCI,WAAW,CAACkC,kBAAkB,CAACtB,UAAU,CAAC;IAC5C,CAAC,MAAM;MACLX,iBAAiB,CAACiC,kBAAkB,CAACtB,UAAU,CAAC;IAClD;EACF,CAAC;EACD;EACAuB,eAAeA,CAAA,EAAG,CAAC;AACrB,CAAC", "ignoreList": []}