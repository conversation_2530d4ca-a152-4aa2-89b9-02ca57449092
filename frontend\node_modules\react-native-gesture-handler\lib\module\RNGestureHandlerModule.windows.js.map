{"version": 3, "names": ["PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "Gestures", "handleSetJSResponder", "_tag", "_blockNativeResponder", "handleClearJSResponder", "createGestureHandler", "_handler<PERSON>ame", "_handlerTag", "_config", "attachGestureHandler", "_new<PERSON>iew", "_actionType", "_propsRef", "updateGestureHandler", "_newConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "sourceRoot": "../../src", "sources": ["RNGestureHandlerModule.windows.ts"], "mappings": ";;AAIA;AACA,OAAOA,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,uBAAuB,MAAM,wCAAwC;AAC5E,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,sBAAsB,MAAM,uCAAuC;AAC1E,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,wBAAwB,MAAM,yCAAyC;AAC9E,OAAOC,oBAAoB,MAAM,qCAAqC;AAGtE,OAAO,MAAMC,QAAQ,GAAG;EACtBF,wBAAwB;EACxBN,iBAAiB;EACjBC,iBAAiB;EACjBC,uBAAuB;EACvBC,mBAAmB;EACnBC,sBAAsB;EACtBC,mBAAmB;EACnBE;AACF,CAAC;AAED,eAAe;EACbE,oBAAoBA,CAACC,IAAY,EAAEC,qBAA8B,EAAE;IACjE;EAAA,CACD;EACDC,sBAAsBA,CAAA,EAAG;IACvB;EAAA,CACD;EACDC,oBAAoBA,CAClBC,YAAmC,EACnCC,WAAmB,EACnBC,OAAU,EACV;IACA;EAAA,CACD;EACDC,oBAAoBA,CAClBF,WAAmB;EACnB;EACAG,QAAa,EACbC,WAAuB,EACvBC,SAAmC,EACnC;IACA;EAAA,CACD;EACDC,oBAAoBA,CAACN,WAAmB,EAAEO,UAAkB,EAAE;IAC5D;EAAA,CACD;EACDC,qBAAqBA,CAACR,WAAmB,EAAE;IACzC;EAAA,CACD;EACDS,kBAAkBA,CAACT,WAAmB,EAAE;IACtC;EAAA,CACD;EACDU,eAAeA,CAAA,EAAG;IAChB;EAAA;AAEJ,CAAC", "ignoreList": []}