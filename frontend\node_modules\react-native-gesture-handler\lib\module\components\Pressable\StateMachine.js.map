{"version": 3, "names": ["PressableStateMachine", "constructor", "states", "currentStepIndex", "eventPayload", "setStates", "reset", "handleEvent", "eventName", "step", "callback", "length"], "sourceRoot": "../../../../src", "sources": ["components/Pressable/StateMachine.tsx"], "mappings": ";;AAOA,MAAMA,qBAAqB,CAAC;EAK1BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;EAEOC,SAASA,CAACH,MAAyB,EAAE;IAC1C,IAAI,CAACA,MAAM,GAAGA,MAAM;EACtB;EAEOI,KAAKA,CAAA,EAAG;IACb,IAAI,CAACH,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;EAEOG,WAAWA,CAACC,SAAiB,EAAEJ,YAA6B,EAAE;IACnE,IAAI,CAAC,IAAI,CAACF,MAAM,EAAE;MAChB;IACF;IAEA,MAAMO,IAAI,GAAG,IAAI,CAACP,MAAM,CAAC,IAAI,CAACC,gBAAgB,CAAC;IAC/C,IAAI,CAACC,YAAY,GAAGA,YAAY,IAAI,IAAI,CAACA,YAAY;IAErD,IAAIK,IAAI,CAACD,SAAS,KAAKA,SAAS,EAAE;MAChC,IAAI,IAAI,CAACL,gBAAgB,GAAG,CAAC,EAAE;QAC7B;QACA,IAAI,CAACG,KAAK,CAAC,CAAC;QACZ,IAAI,CAACC,WAAW,CAACC,SAAS,EAAEJ,YAAY,CAAC;MAC3C;MACA;IACF;IAEA,IAAI,IAAI,CAACA,YAAY,IAAIK,IAAI,CAACC,QAAQ,EAAE;MACtCD,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACN,YAAY,CAAC;IAClC;IAEA,IAAI,CAACD,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACA,gBAAgB,KAAK,IAAI,CAACD,MAAM,CAACS,MAAM,EAAE;MAChD,IAAI,CAACL,KAAK,CAAC,CAAC;IACd;EACF;AACF;AAEA,SAASN,qBAAqB", "ignoreList": []}