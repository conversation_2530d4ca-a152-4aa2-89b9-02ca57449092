{"version": 3, "names": ["React", "forwardRef", "useEffect", "useRef", "Platform", "Text", "RNText", "GestureObjects", "Gesture", "GestureDetector", "jsx", "_jsx", "props", "ref", "onPress", "onLongPress", "rest", "textRef", "native", "Native", "runOnJS", "ref<PERSON><PERSON><PERSON>", "node", "current", "rngh", "OS", "textElement", "setAttribute", "gesture", "children"], "sourceRoot": "../../../src", "sources": ["components/Text.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAEVC,UAAU,EAEVC,SAAS,EACTC,MAAM,QACD,OAAO;AACd,SACEC,QAAQ,EACRC,IAAI,IAAIC,MAAM,QAET,cAAc;AAErB,SAASC,cAAc,IAAIC,OAAO,QAAQ,qCAAqC;AAC/E,SAASC,eAAe,QAAQ,sCAAsC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEvE,OAAO,MAAMN,IAAI,gBAAGJ,UAAU,CAC5B,CACEW,KAAkB,EAClBC,GAAoD,KACjD;EACH,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAE,GAAGC;EAAK,CAAC,GAAGJ,KAAK;EAE/C,MAAMK,OAAO,GAAGd,MAAM,CAAgB,IAAI,CAAC;EAC3C,MAAMe,MAAM,GAAGV,OAAO,CAACW,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC;EAE7C,MAAMC,UAAU,GAAIC,IAAS,IAAK;IAChCL,OAAO,CAACM,OAAO,GAAGD,IAAI;IAEtB,IAAIT,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MAC7BA,GAAG,CAACS,IAAI,CAAC;IACX,CAAC,MAAM;MACLT,GAAG,CAACU,OAAO,GAAGD,IAAI;IACpB;EACF,CAAC;;EAED;EACA;EACA;EACAD,UAAU,CAACG,IAAI,GAAG,IAAI;EAEtBtB,SAAS,CAAC,MAAM;IACd,IAAIE,QAAQ,CAACqB,EAAE,KAAK,KAAK,EAAE;MACzB;IACF;IAEA,MAAMC,WAAW,GAAGb,GAAG,GAClBA,GAAG,CAAkDU,OAAO,GAC7DN,OAAO,CAACM,OAAO;;IAEnB;IACCG,WAAW,EAAgCC,YAAY,CACtD,UAAU,EACV,MACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOb,OAAO,IAAIC,WAAW,gBAC3BJ,IAAA,CAACF,eAAe;IAACmB,OAAO,EAAEV,MAAO;IAAAW,QAAA,eAC/BlB,IAAA,CAACL,MAAM;MACLQ,OAAO,EAAEA,OAAQ;MACjBC,WAAW,EAAEA,WAAY;MACzBF,GAAG,EAAEQ,UAAW;MAAA,GACZL;IAAI,CACT;EAAC,CACa,CAAC,gBAElBL,IAAA,CAACL,MAAM;IAACO,GAAG,EAAEA,GAAI;IAAA,GAAKG;EAAI,CAAG,CAC9B;AACH,CACF,CAAC;AACD", "ignoreList": []}