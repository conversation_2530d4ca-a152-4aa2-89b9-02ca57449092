{"version": 3, "names": ["React", "Component", "GenericTouchable", "TOUCHABLE_STATE", "StyleSheet", "View", "jsx", "_jsx", "TouchableHighlight", "defaultProps", "activeOpacity", "delayPressOut", "underlayColor", "constructor", "props", "state", "extraChildStyle", "extraUnderlayStyle", "showUnderlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setState", "opacity", "backgroundColor", "onShowUnderlay", "onPress", "onPressIn", "onPressOut", "onLongPress", "<PERSON><PERSON><PERSON><PERSON>", "onHideUnderlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "child", "Children", "only", "cloneElement", "style", "compose", "onStateChange", "_from", "to", "BEGAN", "UNDETERMINED", "MOVED_OUTSIDE", "render", "rest"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableHighlight.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,IAAIC,eAAe,QAAQ,oBAAoB;AAEtE,SACEC,UAAU,EACVC,IAAI,QAIC,cAAc;;AAWrB;AACA;AACA;AAFA,SAAAC,GAAA,IAAAC,IAAA;AAMA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,kBAAkB,SAASP,SAAS,CAGvD;EACA,OAAOQ,YAAY,GAAG;IACpB,GAAGP,gBAAgB,CAACO,YAAY;IAChCC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,GAAG;IAClBC,aAAa,EAAE;EACjB,CAAC;EAEDC,WAAWA,CAACC,KAA8B,EAAE;IAC1C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACXC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC;EACH;;EAEA;EACAC,YAAY,GAAGA,CAAA,KAAM;IACnB,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MAC3B;IACF;IACA,IAAI,CAACC,QAAQ,CAAC;MACZJ,eAAe,EAAE;QACfK,OAAO,EAAE,IAAI,CAACP,KAAK,CAACJ;MACtB,CAAC;MACDO,kBAAkB,EAAE;QAClBK,eAAe,EAAE,IAAI,CAACR,KAAK,CAACF;MAC9B;IACF,CAAC,CAAC;IACF,IAAI,CAACE,KAAK,CAACS,cAAc,GAAG,CAAC;EAC/B,CAAC;EAEDJ,eAAe,GAAGA,CAAA,KAChB,IAAI,CAACL,KAAK,CAACU,OAAO,IAClB,IAAI,CAACV,KAAK,CAACW,SAAS,IACpB,IAAI,CAACX,KAAK,CAACY,UAAU,IACrB,IAAI,CAACZ,KAAK,CAACa,WAAW;EAExBC,YAAY,GAAGA,CAAA,KAAM;IACnB,IAAI,CAACR,QAAQ,CAAC;MACZJ,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,IAAI,CAACH,KAAK,CAACe,cAAc,GAAG,CAAC;EAC/B,CAAC;EAEDC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAChB,KAAK,CAACiB,QAAQ,EAAE;MACxB,oBAAOxB,IAAA,CAACF,IAAI,IAAE,CAAC;IACjB;IAEA,MAAM2B,KAAK,GAAGhC,KAAK,CAACiC,QAAQ,CAACC,IAAI,CAC/B,IAAI,CAACpB,KAAK,CAACiB,QACb,CAAkC,CAAC,CAAC;IACpC,oBAAO/B,KAAK,CAACmC,YAAY,CAACH,KAAK,EAAE;MAC/BI,KAAK,EAAEhC,UAAU,CAACiC,OAAO,CAACL,KAAK,CAAClB,KAAK,CAACsB,KAAK,EAAE,IAAI,CAACrB,KAAK,CAACC,eAAe;IACzE,CAAC,CAAC;EACJ;EAEAsB,aAAa,GAAGA,CAACC,KAAa,EAAEC,EAAU,KAAK;IAC7C,IAAIA,EAAE,KAAKrC,eAAe,CAACsC,KAAK,EAAE;MAChC,IAAI,CAACvB,YAAY,CAAC,CAAC;IACrB,CAAC,MAAM,IACLsB,EAAE,KAAKrC,eAAe,CAACuC,YAAY,IACnCF,EAAE,KAAKrC,eAAe,CAACwC,aAAa,EACpC;MACA,IAAI,CAACf,YAAY,CAAC,CAAC;IACrB;EACF,CAAC;EAEDgB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAER,KAAK,GAAG,CAAC,CAAC;MAAE,GAAGS;IAAK,CAAC,GAAG,IAAI,CAAC/B,KAAK;IAC1C,MAAM;MAAEG;IAAmB,CAAC,GAAG,IAAI,CAACF,KAAK;IACzC,oBACER,IAAA,CAACL,gBAAgB;MAAA,GACX2C,IAAI;MACRT,KAAK,EAAE,CAACA,KAAK,EAAEnB,kBAAkB,CAAE;MACnCqB,aAAa,EAAE,IAAI,CAACA,aAAc;MAAAP,QAAA,EACjC,IAAI,CAACD,cAAc,CAAC;IAAC,CACN,CAAC;EAEvB;AACF", "ignoreList": []}