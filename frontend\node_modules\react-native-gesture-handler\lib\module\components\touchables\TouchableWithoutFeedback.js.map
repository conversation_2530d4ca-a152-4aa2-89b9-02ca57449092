{"version": 3, "names": ["React", "GenericTouchable", "jsx", "_jsx", "TouchableWithoutFeedback", "forwardRef", "delayLongPress", "extraButtonProps", "rippleColor", "exclusive", "rest", "ref"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableWithoutFeedback.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,gBAAgB,MAAM,oBAAoB;;AAGjD;AACA;AACA;AAFA,SAAAC,GAAA,IAAAC,IAAA;AAKA;AACA;AACA;AACA,MAAMC,wBAAwB,gBAAGJ,KAAK,CAACK,UAAU,CAI/C,CACE;EACEC,cAAc,GAAG,GAAG;EACpBC,gBAAgB,GAAG;IACjBC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE;EACb,CAAC;EACD,GAAGC;AACL,CAAC,EAEDC,GAAG,kBAEHR,IAAA,CAACF,gBAAgB;EACfU,GAAG,EAAEA,GAAI;EACTL,cAAc,EAAEA,cAAe;EAC/BC,gBAAgB,EAAEA,gBAAiB;EAAA,GAC/BG;AAAI,CACT,CAEL,CAAC;AAED,eAAeN,wBAAwB", "ignoreList": []}