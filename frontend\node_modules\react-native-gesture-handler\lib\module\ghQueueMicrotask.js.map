{"version": 3, "names": ["ghQueueMicrotask", "setImmediate", "bind", "requestAnimationFrame", "queueMicrotask"], "sourceRoot": "../../src", "sources": ["ghQueueMicrotask.ts"], "mappings": ";;AAAA;AACA;AACA,OAAO,MAAMA,gBAAgB,GAC3B,OAAOC,YAAY,KAAK,UAAU,GAC9BA,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,GACvB,OAAOC,qBAAqB,KAAK,UAAU,GACzCA,qBAAqB,CAACD,IAAI,CAAC,IAAI,CAAC,GAChCE,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}