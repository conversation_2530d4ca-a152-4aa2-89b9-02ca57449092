{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "pinchHandlerName", "PinchGestureHandler", "name", "allowedProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/PinchGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;;AAE/B;AACA;AACA;;AAIA,OAAO,MAAMC,gBAAgB,GAAG,qBAAqB;;AAErD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGH,aAAa,CAG9C;EACAI,IAAI,EAAEF,gBAAgB;EACtBG,YAAY,EAAEJ,uBAAuB;EACrCK,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}