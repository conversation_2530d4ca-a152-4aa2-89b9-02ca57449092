{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "rotationHandlerName", "RotationGestureHandler", "name", "allowedProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/RotationGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;;AAE/B;AACA;AACA;;AAIA,OAAO,MAAMC,mBAAmB,GAAG,wBAAwB;;AAE3D;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAGH,aAAa,CAGjD;EACAI,IAAI,EAAEF,mBAAmB;EACzBG,YAAY,EAAEJ,uBAAuB;EACrCK,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}