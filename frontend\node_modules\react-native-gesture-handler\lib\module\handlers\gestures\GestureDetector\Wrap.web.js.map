{"version": 3, "names": ["React", "forwardRef", "tagMessage", "isRNSVGNode", "jsx", "_jsx", "Wrap", "children", "ref", "child", "Children", "only", "clone", "cloneElement", "props", "style", "display", "e", "Error", "AnimatedWrap"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/Wrap.web.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEjD,OAAO,MAAMC,IAAI,gBAAGL,UAAU,CAC5B,CAAC;EAAEM;AAAS,CAAC,EAAEC,GAAG,KAAK;EACrB,IAAI;IACF;IACA,MAAMC,KAAU,GAAGT,KAAK,CAACU,QAAQ,CAACC,IAAI,CAACJ,QAAQ,CAAC;IAEhD,IAAIJ,WAAW,CAACM,KAAK,CAAC,EAAE;MACtB,MAAMG,KAAK,gBAAGZ,KAAK,CAACa,YAAY,CAC9BJ,KAAK,EACL;QAAED;MAAI,CAAC;MACP;MACAC,KAAK,CAACK,KAAK,CAACP,QACd,CAAC;MAED,OAAOK,KAAK;IACd;IAEA,oBACEP,IAAA;MACEG,GAAG,EAAEA,GAAiC;MACtCO,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAW,CAAE;MAAAT,QAAA,EAC9BE;IAAK,CACH,CAAC;EAEV,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACV,MAAM,IAAIC,KAAK,CACbhB,UAAU,CACR,2KACF,CACF,CAAC;EACH;AACF,CACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMiB,YAAY,GAAGb,IAAI", "ignoreList": []}