{"version": 3, "names": ["startListening", "RNGestureHandlerModule", "isF<PERSON><PERSON>", "fabricInitialized", "initialize", "maybeInitializeFabric", "install"], "sourceRoot": "../../src", "sources": ["init.ts"], "mappings": ";;AAAA,SAASA,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,QAAQ,QAAQ,SAAS;AAElC,IAAIC,iBAAiB,GAAG,KAAK;AAE7B,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3BJ,cAAc,CAAC,CAAC;AAClB;;AAEA;AACA;AACA,OAAO,SAASK,qBAAqBA,CAAA,EAAG;EACtC,IAAIH,QAAQ,CAAC,CAAC,IAAI,CAACC,iBAAiB,EAAE;IACpCF,sBAAsB,CAACK,OAAO,CAAC,CAAC;IAChCH,iBAAiB,GAAG,IAAI;EAC1B;AACF", "ignoreList": []}