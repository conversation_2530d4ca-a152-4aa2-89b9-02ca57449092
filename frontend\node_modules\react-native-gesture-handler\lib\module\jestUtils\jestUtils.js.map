{"version": 3, "names": ["invariant", "DeviceEventEmitter", "flingHandlerName", "forceTouchHandlerName", "BaseGesture", "findHandlerByTestID", "longPressHandlerName", "nativeViewHandlerName", "panHandlerName", "pinchHandlerName", "rotationHandlerName", "tapHandlerName", "State", "hasProperty", "withPrevAndCurrent", "fireEvent", "_element", "_name", "_data", "require", "_e", "handlersDefaultEvents", "x", "y", "absoluteX", "absoluteY", "numberOfPointers", "force", "duration", "pointerInside", "translationX", "translationY", "velocityX", "velocityY", "stylusData", "undefined", "focalX", "focalY", "scale", "velocity", "anchorX", "anchorY", "rotation", "isGesture", "componentOrGesture", "wrapWithNativeEvent", "event", "nativeEvent", "fillOldStateChanges", "previousEvent", "currentEvent", "isFirstEvent", "oldState", "UNDETERMINED", "isGestureStateEvent", "state", "validateStateTransitions", "stringify", "JSON", "errorMsgWithBothEvents", "description", "errorMsgWithCurrentEvent", "BEGAN", "fillMissingDefaultsFor", "handlerType", "handlerTag", "isDiscreteHandler", "fillMissingStatesTransitions", "events", "_events", "lastEvent", "length", "firstEvent", "shouldDuplicateFirstEvent", "hasState", "duplicated", "unshift", "shouldDuplicateLastEvent", "END", "FAILED", "CANCELLED", "push", "isWithoutState", "noEventsLeft", "trueFn", "fillEventsForCurrentState", "shouldConsumeEvent", "shouldTransitionToNextState", "peekCurrentEvent", "peekNextEvent", "consumeCurrentEvent", "shift", "nextEvent", "currentRequiredState", "REQUIRED_EVENTS", "currentStateIdx", "eventData", "shouldUseEvent", "transformedEvents", "ACTIVE", "hasAllStates", "iterations", "nextRequiredState", "e", "getHandlerData", "gesture", "emitEvent", "eventName", "args", "emit", "handler<PERSON>ame", "enabled", "config", "gestureHandlerComponent", "props", "fireGestureHandler", "eventList", "_", "map", "lastSentEvent", "hasChangedState", "getByGestureTestId", "testID", "handler", "Error"], "sourceRoot": "../../../src", "sources": ["jestUtils/jestUtils.ts"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,WAAW;AACjC,SAASC,kBAAkB,QAAQ,cAAc;AAEjD,SAEEC,gBAAgB,QACX,iCAAiC;AACxC,SAEEC,qBAAqB,QAChB,sCAAsC;AAQ7C,SAASC,WAAW,QAAqB,8BAA8B;AAOvE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAEEC,oBAAoB,QACf,qCAAqC;AAW5C,SAEEC,qBAAqB,QAChB,sCAAsC;AAC7C,SAEEC,cAAc,QACT,+BAA+B;AACtC,SAEEC,gBAAgB,QACX,iCAAiC;AACxC,SAEEC,mBAAmB,QACd,oCAAoC;AAC3C,SAEEC,cAAc,QACT,+BAA+B;AACtC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,UAAU;;AAE1D;AACA,IAAIC,SAAS,GAAGA,CACdC,QAA2B,EAC3BC,KAAa,EACb,GAAGC,KAAY,KACZ;EACH;AAAA,CACD;AAED,IAAI;EACF;EACAH,SAAS,GAAGI,OAAO,CAAC,+BAA+B,CAAC,CAACJ,SAAS;AAChE,CAAC,CAAC,OAAOK,EAAE,EAAE;EACX;AAAA;AA0BF,MAAMC,qBAA2C,GAAG;EAClD,CAACnB,gBAAgB,GAAG;IAClBoB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACvB,qBAAqB,GAAG;IACvBmB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZE,KAAK,EAAE,CAAC;IACRD,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACpB,oBAAoB,GAAG;IACtBgB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZG,QAAQ,EAAE,GAAG;IACbF,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACnB,qBAAqB,GAAG;IACvBsB,aAAa,EAAE,IAAI;IACnBH,gBAAgB,EAAE;EACpB,CAAC;EACD,CAAClB,cAAc,GAAG;IAChBc,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZK,YAAY,EAAE,GAAG;IACjBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZP,gBAAgB,EAAE,CAAC;IACnBQ,UAAU,EAAEC;EACd,CAAC;EACD,CAAC1B,gBAAgB,GAAG;IAClB2B,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXb,gBAAgB,EAAE;EACpB,CAAC;EACD,CAAChB,mBAAmB,GAAG;IACrB8B,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,IAAI;IACdH,QAAQ,EAAE,CAAC;IACXb,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACf,cAAc,GAAG;IAChBW,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,SAASiB,SAASA,CAChBC,kBAAmD,EAChB;EACnC,OAAOA,kBAAkB,YAAYxC,WAAW;AAClD;AAKA,SAASyC,mBAAmBA,CAC1BC,KAA8B,EACE;EAChC,OAAO;IAAEC,WAAW,EAAED;EAAM,CAAC;AAC/B;AAEA,SAASE,mBAAmBA,CAC1BC,aAA6C,EAC7CC,YAAuD,EAC9B;EACzB,MAAMC,YAAY,GAAGF,aAAa,KAAK,IAAI;EAC3C,IAAIE,YAAY,EAAE;IAChB,OAAO;MACLC,QAAQ,EAAExC,KAAK,CAACyC,YAAY;MAC5B,GAAGH;IACL,CAAC;EACH;EAEA,MAAMI,mBAAmB,GAAGL,aAAa,CAACM,KAAK,KAAKL,YAAY,CAACK,KAAK;EACtE,IAAID,mBAAmB,EAAE;IACvB,OAAO;MACLF,QAAQ,EAAEH,aAAa,EAAEM,KAAK;MAC9B,GAAGL;IACL,CAAC;EACH,CAAC,MAAM;IACL,OAAOA,YAAY;EACrB;AACF;AAKA,SAASM,wBAAwBA,CAC/BP,aAAqC,EACrCC,YAA6B,EAC7B;EACA,SAASO,SAASA,CAACX,KAAqC,EAAE;IACxD,OAAOY,IAAI,CAACD,SAAS,CAACX,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;EACvC;EACA,SAASa,sBAAsBA,CAACC,WAAmB,EAAE;IACnD,OAAO,GAAGA,WAAW,oBAAoBH,SAAS,CAChDP,YACF,CAAC,qBAAqBO,SAAS,CAACR,aAAa,CAAC,EAAE;EAClD;EAEA,SAASY,wBAAwBA,CAACD,WAAmB,EAAE;IACrD,OAAO,GAAGA,WAAW,oBAAoBH,SAAS,CAACP,YAAY,CAAC,EAAE;EACpE;EAEAlD,SAAS,CACPa,WAAW,CAACqC,YAAY,EAAE,OAAO,CAAC,EAClCW,wBAAwB,CAAC,6BAA6B,CACxD,CAAC;EAED,MAAMV,YAAY,GAAGF,aAAa,KAAK,IAAI;EAC3C,IAAIE,YAAY,EAAE;IAChBnD,SAAS,CACPkD,YAAY,CAACK,KAAK,KAAK3C,KAAK,CAACkD,KAAK,EAClCD,wBAAwB,CAAC,mCAAmC,CAC9D,CAAC;EACH;EAEA,IAAIZ,aAAa,KAAK,IAAI,EAAE;IAC1B,IAAIA,aAAa,CAACM,KAAK,KAAKL,YAAY,CAACK,KAAK,EAAE;MAC9CvD,SAAS,CACPa,WAAW,CAACqC,YAAY,EAAE,UAAU,CAAC,EACrCW,wBAAwB,CACtB,sDACF,CACF,CAAC;MACD7D,SAAS,CACPkD,YAAY,CAACE,QAAQ,KAAKH,aAAa,CAACM,KAAK,EAC7CI,sBAAsB,CACpB,0EACF,CACF,CAAC;IACH;EACF;EAEA,OAAOT,YAAY;AACrB;AAOA,SAASa,sBAAsBA,CAAC;EAC9BC,WAAW;EACXC;AACW,CAAC,EAEU;EACtB,OAAQnB,KAAK,IAAK;IAChB,OAAO;MACL,GAAGzB,qBAAqB,CAAC2C,WAAW,CAAC;MACrC,GAAGlB,KAAK;MACRmB;IACF,CAAC;EACH,CAAC;AACH;AAEA,SAASC,iBAAiBA,CAACF,WAAyB,EAAE;EACpD,OACEA,WAAW,KAAK,mBAAmB,IACnCA,WAAW,KAAK,yBAAyB;AAE7C;AAEA,SAASG,4BAA4BA,CACnCC,MAA4B,EAC5BF,iBAA0B,EACJ;EAEtB,MAAMG,OAAO,GAAG,CAAC,GAAGD,MAAM,CAAC;EAC3B,MAAME,SAAS,GAAGD,OAAO,CAACA,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;EACrD,MAAMC,UAAU,GAAGH,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;EAErC,MAAMI,yBAAyB,GAC7B,CAACP,iBAAiB,IAAI,CAACQ,QAAQ,CAAC9D,KAAK,CAACkD,KAAK,CAAC,CAACU,UAAU,CAAC;EAC1D,IAAIC,yBAAyB,EAAE;IAC7B,MAAME,UAAU,GAAG;MAAE,GAAGH,UAAU;MAAEjB,KAAK,EAAE3C,KAAK,CAACkD;IAAM,CAAC;IACxD;IACA,OAAOa,UAAU,CAACvB,QAAQ;IAC1BiB,OAAO,CAACO,OAAO,CAACD,UAAU,CAAC;EAC7B;EAEA,MAAME,wBAAwB,GAC5B,CAACH,QAAQ,CAAC9D,KAAK,CAACkE,GAAG,CAAC,CAACR,SAAS,CAAC,IAC/B,CAACI,QAAQ,CAAC9D,KAAK,CAACmE,MAAM,CAAC,CAACT,SAAS,CAAC,IAClC,CAACI,QAAQ,CAAC9D,KAAK,CAACoE,SAAS,CAAC,CAACV,SAAS,CAAC;EAEvC,IAAIO,wBAAwB,EAAE;IAC5B,MAAMF,UAAU,GAAG;MAAE,GAAGL,SAAS;MAAEf,KAAK,EAAE3C,KAAK,CAACkE;IAAI,CAAC;IACrD;IACA,OAAOH,UAAU,CAACvB,QAAQ;IAC1BiB,OAAO,CAACY,IAAI,CAACN,UAAU,CAAC;EAC1B;EAEA,SAASO,cAAcA,CAACpC,KAAY,EAAE;IACpC,OAAOA,KAAK,KAAK,IAAI,IAAI,CAACjC,WAAW,CAACiC,KAAK,EAAE,OAAO,CAAC;EACvD;EACA,SAAS4B,QAAQA,CAACnB,KAAY,EAAE;IAC9B,OAAQT,KAAY,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACS,KAAK,KAAKA,KAAK;EAClE;EACA,SAAS4B,YAAYA,CAACrC,KAAY,EAAE;IAClC,OAAOA,KAAK,KAAK,IAAI;EACvB;EAEA,SAASsC,MAAMA,CAAA,EAAG;IAChB,OAAO,IAAI;EACb;EAKA,SAASC,yBAAyBA,CAAC;IACjCC,kBAAkB,GAAGF,MAAM;IAC3BG,2BAA2B,GAAGH;EAC1B,CAAC,EAAE;IACP,SAASI,gBAAgBA,CAAA,EAAU;MACjC,OAAOnB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAC3B;IACA,SAASoB,aAAaA,CAAA,EAAU;MAC9B,OAAOpB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAC3B;IACA,SAASqB,mBAAmBA,CAAA,EAAG;MAC7BrB,OAAO,CAACsB,KAAK,CAAC,CAAC;IACjB;IACA,MAAMzC,YAAY,GAAGsC,gBAAgB,CAAC,CAAC;IACvC,MAAMI,SAAS,GAAGH,aAAa,CAAC,CAAC;IACjC,MAAMI,oBAAoB,GAAGC,eAAe,CAACC,eAAe,CAAC;IAE7D,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,MAAMC,cAAc,GAAGX,kBAAkB,CAACpC,YAAY,CAAC;IACvD,IAAI+C,cAAc,EAAE;MAClBD,SAAS,GAAG9C,YAAa;MACzBwC,mBAAmB,CAAC,CAAC;IACvB;IACAQ,iBAAiB,CAACjB,IAAI,CAAC;MAAE1B,KAAK,EAAEsC,oBAAoB;MAAE,GAAGG;IAAU,CAAC,CAAC;IACrE,IAAIT,2BAA2B,CAACK,SAAS,CAAC,EAAE;MAC1CG,eAAe,EAAE;IACnB;EACF;EAEA,MAAMD,eAAe,GAAG,CAAClF,KAAK,CAACkD,KAAK,EAAElD,KAAK,CAACuF,MAAM,EAAEvF,KAAK,CAACkE,GAAG,CAAC;EAE9D,IAAIiB,eAAe,GAAG,CAAC;EACvB,MAAMG,iBAAuC,GAAG,EAAE;EAClD,IAAIE,YAAY;EAChB,IAAIC,UAAU,GAAG,CAAC;EAClB,GAAG;IACD,MAAMC,iBAAiB,GAAGR,eAAe,CAACC,eAAe,CAAC;IAC1D,IAAIO,iBAAiB,KAAK1F,KAAK,CAACkD,KAAK,EAAE;MACrCuB,yBAAyB,CAAC;QACxBC,kBAAkB,EAAGiB,CAAQ,IAC3BrB,cAAc,CAACqB,CAAC,CAAC,IAAI7B,QAAQ,CAAC9D,KAAK,CAACkD,KAAK,CAAC,CAACyC,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAID,iBAAiB,KAAK1F,KAAK,CAACuF,MAAM,EAAE;MAC7C,MAAMb,kBAAkB,GAAIiB,CAAQ,IAClCrB,cAAc,CAACqB,CAAC,CAAC,IAAI7B,QAAQ,CAAC9D,KAAK,CAACuF,MAAM,CAAC,CAACI,CAAC,CAAC;MAChD,MAAMhB,2BAA2B,GAAIK,SAAgB,IACnDT,YAAY,CAACS,SAAS,CAAC,IACvBlB,QAAQ,CAAC9D,KAAK,CAACkE,GAAG,CAAC,CAACc,SAAS,CAAC,IAC9BlB,QAAQ,CAAC9D,KAAK,CAACmE,MAAM,CAAC,CAACa,SAAS,CAAC,IACjClB,QAAQ,CAAC9D,KAAK,CAACoE,SAAS,CAAC,CAACY,SAAS,CAAC;MAEtCP,yBAAyB,CAAC;QACxBC,kBAAkB;QAClBC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,iBAAiB,KAAK1F,KAAK,CAACkE,GAAG,EAAE;MAC1CO,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC/B;IACAe,YAAY,GAAGL,eAAe,KAAKD,eAAe,CAACvB,MAAM;IAEzDvE,SAAS,CACPqG,UAAU,EAAE,IAAI,GAAG,EACnB,+FACF,CAAC;EACH,CAAC,QAAQ,CAACD,YAAY;EAEtB,OAAOF,iBAAiB;AAC1B;AAYA,SAASM,cAAcA,CACrB5D,kBAAmD,EACtC;EACb,IAAID,SAAS,CAACC,kBAAkB,CAAC,EAAE;IACjC,MAAM6D,OAAO,GAAG7D,kBAAkB;IAClC,OAAO;MACL8D,SAAS,EAAEA,CAACC,SAAS,EAAEC,IAAI,KAAK;QAC9B3G,kBAAkB,CAAC4G,IAAI,CAACF,SAAS,EAAEC,IAAI,CAAC7D,WAAW,CAAC;MACtD,CAAC;MACDiB,WAAW,EAAEyC,OAAO,CAACK,WAA2B;MAChD7C,UAAU,EAAEwC,OAAO,CAACxC,UAAU;MAC9B8C,OAAO,EAAEN,OAAO,CAACO,MAAM,CAACD;IAC1B,CAAC;EACH;EACA,MAAME,uBAAuB,GAAGrE,kBAAkB;EAClD,OAAO;IACL8D,SAAS,EAAEA,CAACC,SAAS,EAAEC,IAAI,KAAK;MAC9B7F,SAAS,CAACkG,uBAAuB,EAAEN,SAAS,EAAEC,IAAI,CAAC;IACrD,CAAC;IACD5C,WAAW,EAAEiD,uBAAuB,CAACC,KAAK,CAAClD,WAA2B;IACtEC,UAAU,EAAEgD,uBAAuB,CAACC,KAAK,CAACjD,UAAoB;IAC9D8C,OAAO,EAAEE,uBAAuB,CAACC,KAAK,CAACH;EACzC,CAAC;AACH;;AAqBA;;AAaA,OAAO,SAASI,kBAAkBA,CAChCvE,kBAAmD,EACnDwE,SAAsE,GAAG,EAAE,EACrE;EACN,MAAM;IAAEV,SAAS;IAAE1C,WAAW;IAAEC,UAAU;IAAE8C;EAAQ,CAAC,GACnDP,cAAc,CAAC5D,kBAAkB,CAAC;EAEpC,IAAImE,OAAO,KAAK,KAAK,EAAE;IACrB;EACF;EAEA,IAAIM,CAAC,GAAGlD,4BAA4B,CAClCiD,SAAS,EACTlD,iBAAiB,CAACF,WAAW,CAC/B,CAAC;EACDqD,CAAC,GAAGA,CAAC,CAACC,GAAG,CAACvD,sBAAsB,CAAC;IAAEE,UAAU;IAAED;EAAY,CAAC,CAAC,CAAC;EAC9DqD,CAAC,GAAGvG,kBAAkB,CAACuG,CAAC,EAAErE,mBAAmB,CAAC;EAC9CqE,CAAC,GAAGvG,kBAAkB,CAACuG,CAAC,EAAE7D,wBAAwB,CAAC;EACnD;EACA6D,CAAC,GAAGA,CAAC,CAACC,GAAG,CAACzE,mBAAmB,CAAC;EAE9B,MAAMuB,MAAM,GAAGiD,CAAgD;EAE/D,MAAM7C,UAAU,GAAGJ,MAAM,CAACuB,KAAK,CAAC,CAAE;EAElCe,SAAS,CAAC,6BAA6B,EAAElC,UAAU,CAAC;EACpD,IAAI+C,aAAa,GAAG/C,UAAU;EAC9B,KAAK,MAAM1B,KAAK,IAAIsB,MAAM,EAAE;IAC1B,MAAMoD,eAAe,GACnBD,aAAa,CAACxE,WAAW,CAACQ,KAAK,KAAKT,KAAK,CAACC,WAAW,CAACQ,KAAK;IAE7D,IAAIiE,eAAe,EAAE;MACnBd,SAAS,CAAC,6BAA6B,EAAE5D,KAAK,CAAC;IACjD,CAAC,MAAM;MACL4D,SAAS,CAAC,uBAAuB,EAAE5D,KAAK,CAAC;IAC3C;IACAyE,aAAa,GAAGzE,KAAK;EACvB;AACF;AAEA,OAAO,SAAS2E,kBAAkBA,CAACC,MAAc,EAAE;EACjD,MAAMC,OAAO,GAAGtH,mBAAmB,CAACqH,MAAM,CAAC;EAC3C,IAAIC,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,qBAAqBF,MAAM,mBAAmB,CAAC;EACjE;EACA,OAAOC,OAAO;AAChB", "ignoreList": []}