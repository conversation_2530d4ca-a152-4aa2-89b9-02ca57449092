{"version": 3, "names": ["React", "TouchableHighlight", "TouchableNativeFeedback", "TouchableOpacity", "TouchableWithoutFeedback", "ScrollView", "FlatList", "Switch", "TextInput", "DrawerLayoutAndroid", "View", "State", "Directions", "jsx", "_jsx", "NOOP", "PanGestureHandler", "attachGestureHandler", "createGestureHandler", "dropGestureHandler", "updateGestureHandler", "flushOperations", "install", "NativeViewGestureHandler", "TapGestureHandler", "ForceTouchGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "RawButton", "enabled", "rest", "disabled", "children", "BaseButton", "RectButton", "BorderlessButton"], "sourceRoot": "../../../src", "sources": ["mocks/mocks.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,kBAAkB,EAClBC,uBAAuB,EACvBC,gBAAgB,EAChBC,wBAAwB,EACxBC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,mBAAmB,EACnBC,IAAI,QACC,cAAc;AACrB,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,UAAU,QAAQ,eAAe;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE3C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB;AAAA,CACD;AACD,MAAMC,iBAAiB,GAAGN,IAAI;AAC9B,MAAMO,oBAAoB,GAAGF,IAAI;AACjC,MAAMG,oBAAoB,GAAGH,IAAI;AACjC,MAAMI,kBAAkB,GAAGJ,IAAI;AAC/B,MAAMK,oBAAoB,GAAGL,IAAI;AACjC,MAAMM,eAAe,GAAGN,IAAI;AAC5B,MAAMO,OAAO,GAAGP,IAAI;AACpB,MAAMQ,wBAAwB,GAAGb,IAAI;AACrC,MAAMc,iBAAiB,GAAGd,IAAI;AAC9B,MAAMe,wBAAwB,GAAGf,IAAI;AACrC,MAAMgB,uBAAuB,GAAGhB,IAAI;AACpC,MAAMiB,mBAAmB,GAAGjB,IAAI;AAChC,MAAMkB,sBAAsB,GAAGlB,IAAI;AACnC,MAAMmB,mBAAmB,GAAGnB,IAAI;AAChC,OAAO,MAAMoB,SAAS,GAAGA,CAAC;EAAEC,OAAO;EAAE,GAAGC;AAAU,CAAC,kBACjDlB,IAAA,CAACZ,uBAAuB;EAAC+B,QAAQ,EAAEF,OAAO,KAAK,KAAM;EAAA,GAAKC,IAAI;EAAAE,QAAA,eAC5DpB,IAAA,CAACJ,IAAI,IAAE;AAAC,CACe,CAC1B;AACD,OAAO,MAAMyB,UAAU,GAAGL,SAAS;AACnC,OAAO,MAAMM,UAAU,GAAGN,SAAS;AACnC,OAAO,MAAMO,gBAAgB,GAAGnC,uBAAuB;AAEvD,eAAe;EACbD,kBAAkB;EAClBC,uBAAuB;EACvBC,gBAAgB;EAChBC,wBAAwB;EACxBC,UAAU;EACVC,QAAQ;EACRC,MAAM;EACNC,SAAS;EACTC,mBAAmB;EACnBc,wBAAwB;EACxBC,iBAAiB;EACjBC,wBAAwB;EACxBC,uBAAuB;EACvBC,mBAAmB;EACnBC,sBAAsB;EACtBC,mBAAmB;EACnBb,iBAAiB;EACjBC,oBAAoB;EACpBC,oBAAoB;EACpBC,kBAAkB;EAClBC,oBAAoB;EACpBC,eAAe;EACfC,OAAO;EACP;EACAV,UAAU;EACVD;AACF,CAAC", "ignoreList": []}