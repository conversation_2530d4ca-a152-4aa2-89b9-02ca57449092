{"version": 3, "names": ["DEFAULT_TOUCH_SLOP", "EventTypes", "ScaleGestureDetector", "inProgress", "constructor", "callbacks", "onScaleBegin", "onScale", "onScaleEnd", "spanSlop", "minSpan", "onTouchEvent", "event", "tracker", "currentTime", "time", "action", "eventType", "numOfPointers", "trackedPointersCount", "streamComplete", "UP", "ADDITIONAL_POINTER_UP", "CANCEL", "DOWN", "initialSpan", "config<PERSON><PERSON><PERSON>", "ADDITIONAL_POINTER_DOWN", "pointerUp", "ignoredPointer", "pointerId", "undefined", "div", "coordsSum", "getAbsoluteCoordsSum", "focusX", "x", "focusY", "y", "devSumX", "devSumY", "trackedPointers", "for<PERSON>ach", "value", "key", "Math", "abs", "abosoluteCoords", "devX", "devY", "spanX", "spanY", "span", "hypot", "wasInProgress", "_focusX", "_focusY", "prevSpan", "_currentSpan", "prevTime", "MOVE", "currentSpan", "calculateScaleFactor", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../../src", "sources": ["web/detectors/ScaleGestureDetector.ts"], "mappings": ";;AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAAuBC,UAAU,QAAQ,eAAe;AAUxD,eAAe,MAAMC,oBAAoB,CAAiC;EAehEC,UAAU,GAAG,KAAK;EAK1BC,WAAWA,CAACC,SAA+B,EAAE;IAC3C,IAAI,CAACC,YAAY,GAAGD,SAAS,CAACC,YAAY;IAC1C,IAAI,CAACC,OAAO,GAAGF,SAAS,CAACE,OAAO;IAChC,IAAI,CAACC,UAAU,GAAGH,SAAS,CAACG,UAAU;IAEtC,IAAI,CAACC,QAAQ,GAAGT,kBAAkB,GAAG,CAAC;IACtC,IAAI,CAACU,OAAO,GAAG,CAAC;EAClB;EAEOC,YAAYA,CAACC,KAAmB,EAAEC,OAAuB,EAAW;IACzE,IAAI,CAACC,WAAW,GAAGF,KAAK,CAACG,IAAI;IAE7B,MAAMC,MAAkB,GAAGJ,KAAK,CAACK,SAAS;IAC1C,MAAMC,aAAa,GAAGL,OAAO,CAACM,oBAAoB;IAElD,MAAMC,cAAuB,GAC3BJ,MAAM,KAAKf,UAAU,CAACoB,EAAE,IACxBL,MAAM,KAAKf,UAAU,CAACqB,qBAAqB,IAC3CN,MAAM,KAAKf,UAAU,CAACsB,MAAM;IAE9B,IAAIP,MAAM,KAAKf,UAAU,CAACuB,IAAI,IAAIJ,cAAc,EAAE;MAChD,IAAI,IAAI,CAACjB,UAAU,EAAE;QACnB,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC;QACrB,IAAI,CAACL,UAAU,GAAG,KAAK;QACvB,IAAI,CAACsB,WAAW,GAAG,CAAC;MACtB;MAEA,IAAIL,cAAc,EAAE;QAClB,OAAO,IAAI;MACb;IACF;IAEA,MAAMM,aAAsB,GAC1BV,MAAM,KAAKf,UAAU,CAACuB,IAAI,IAC1BR,MAAM,KAAKf,UAAU,CAACqB,qBAAqB,IAC3CN,MAAM,KAAKf,UAAU,CAAC0B,uBAAuB;IAE/C,MAAMC,SAAS,GAAGZ,MAAM,KAAKf,UAAU,CAACqB,qBAAqB;IAE7D,MAAMO,cAAkC,GAAGD,SAAS,GAChDhB,KAAK,CAACkB,SAAS,GACfC,SAAS;;IAEb;;IAEA,MAAMC,GAAW,GAAGJ,SAAS,GAAGV,aAAa,GAAG,CAAC,GAAGA,aAAa;IAEjE,MAAMe,SAAS,GAAGpB,OAAO,CAACqB,oBAAoB,CAAC,CAAC;IAEhD,MAAMC,MAAM,GAAGF,SAAS,CAACG,CAAC,GAAGJ,GAAG;IAChC,MAAMK,MAAM,GAAGJ,SAAS,CAACK,CAAC,GAAGN,GAAG;;IAEhC;;IAEA,IAAIO,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IAEf3B,OAAO,CAAC4B,eAAe,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;MAC9C,IAAIA,GAAG,KAAKf,cAAc,EAAE;QAC1B;MACF;MAEAU,OAAO,IAAIM,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,eAAe,CAACX,CAAC,GAAGD,MAAM,CAAC;MACrDK,OAAO,IAAIK,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,eAAe,CAACT,CAAC,GAAGD,MAAM,CAAC;IACvD,CAAC,CAAC;IAEF,MAAMW,IAAY,GAAGT,OAAO,GAAGP,GAAG;IAClC,MAAMiB,IAAY,GAAGT,OAAO,GAAGR,GAAG;IAElC,MAAMkB,KAAa,GAAGF,IAAI,GAAG,CAAC;IAC9B,MAAMG,KAAa,GAAGF,IAAI,GAAG,CAAC;IAE9B,MAAMG,IAAI,GAAGP,IAAI,CAACQ,KAAK,CAACH,KAAK,EAAEC,KAAK,CAAC;;IAErC;IACA,MAAMG,aAAsB,GAAG,IAAI,CAACnD,UAAU;IAC9C,IAAI,CAACoD,OAAO,GAAGpB,MAAM;IACrB,IAAI,CAACqB,OAAO,GAAGnB,MAAM;IAErB,IAAI,IAAI,CAAClC,UAAU,KAAKiD,IAAI,GAAG,IAAI,CAAC1C,OAAO,IAAIgB,aAAa,CAAC,EAAE;MAC7D,IAAI,CAAClB,UAAU,CAAC,IAAI,CAAC;MACrB,IAAI,CAACL,UAAU,GAAG,KAAK;MACvB,IAAI,CAACsB,WAAW,GAAG2B,IAAI;IACzB;IAEA,IAAI1B,aAAa,EAAE;MACjB,IAAI,CAACD,WAAW,GAAG,IAAI,CAACgC,QAAQ,GAAG,IAAI,CAACC,YAAY,GAAGN,IAAI;IAC7D;IAEA,IACE,CAAC,IAAI,CAACjD,UAAU,IAChBiD,IAAI,IAAI,IAAI,CAAC1C,OAAO,KACnB4C,aAAa,IAAIT,IAAI,CAACC,GAAG,CAACM,IAAI,GAAG,IAAI,CAAC3B,WAAW,CAAC,GAAG,IAAI,CAAChB,QAAQ,CAAC,EACpE;MACA,IAAI,CAACgD,QAAQ,GAAG,IAAI,CAACC,YAAY,GAAGN,IAAI;MACxC,IAAI,CAACO,QAAQ,GAAG,IAAI,CAAC7C,WAAW;MAChC,IAAI,CAACX,UAAU,GAAG,IAAI,CAACG,YAAY,CAAC,IAAI,CAAC;IAC3C;;IAEA;IACA,IAAIU,MAAM,KAAKf,UAAU,CAAC2D,IAAI,EAAE;MAC9B,OAAO,IAAI;IACb;IAEA,IAAI,CAACF,YAAY,GAAGN,IAAI;IAExB,IAAI,IAAI,CAACjD,UAAU,IAAI,CAAC,IAAI,CAACI,OAAO,CAAC,IAAI,CAAC,EAAE;MAC1C,OAAO,IAAI;IACb;IAEA,IAAI,CAACkD,QAAQ,GAAG,IAAI,CAACI,WAAW;IAChC,IAAI,CAACF,QAAQ,GAAG,IAAI,CAAC7C,WAAW;IAEhC,OAAO,IAAI;EACb;EAEOgD,oBAAoBA,CAAC5C,aAAqB,EAAU;IACzD,IAAIA,aAAa,GAAG,CAAC,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,OAAO,IAAI,CAACuC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACI,WAAW,GAAG,IAAI,CAACJ,QAAQ,GAAG,CAAC;EACjE;EAEA,IAAWI,WAAWA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACH,YAAY;EAC1B;EAEA,IAAWvB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACoB,OAAO;EACrB;EAEA,IAAWlB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACmB,OAAO;EACrB;EAEA,IAAWO,SAASA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACjD,WAAW,GAAG,IAAI,CAAC6C,QAAQ;EACzC;AACF", "ignoreList": []}