{"version": 3, "names": ["Gesture<PERSON>andler", "ManualGestureHandler", "onPointerDown", "event", "tracker", "addToTracker", "begin", "tryToSendTouchEvent", "onPointerAdd", "onPointerMove", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "onPointerRemove"], "sourceRoot": "../../../../src", "sources": ["web/handlers/ManualGestureHandler.ts"], "mappings": ";;AACA,OAAOA,cAAc,MAAM,kBAAkB;AAE7C,eAAe,MAAMC,oBAAoB,SAASD,cAAc,CAAC;EACrDE,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACG,KAAK,CAAC,CAAC;IAEZ,IAAI,CAACC,mBAAmB,CAACJ,KAAK,CAAC;EACjC;EAEUK,YAAYA,CAACL,KAAmB,EAAQ;IAChD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACK,YAAY,CAACL,KAAK,CAAC;EAC3B;EAEUM,aAAaA,CAACN,KAAmB,EAAQ;IACjD,IAAI,CAACC,OAAO,CAACM,KAAK,CAACP,KAAK,CAAC;IACzB,KAAK,CAACM,aAAa,CAACN,KAAK,CAAC;EAC5B;EAEUQ,oBAAoBA,CAACR,KAAmB,EAAQ;IACxD,IAAI,CAACC,OAAO,CAACM,KAAK,CAACP,KAAK,CAAC;IACzB,KAAK,CAACQ,oBAAoB,CAACR,KAAK,CAAC;EACnC;EAEUS,WAAWA,CAACT,KAAmB,EAAQ;IAC/C,KAAK,CAACS,WAAW,CAACT,KAAK,CAAC;IACxB,IAAI,CAACC,OAAO,CAACS,iBAAiB,CAACV,KAAK,CAACW,SAAS,CAAC;EACjD;EAEUC,eAAeA,CAACZ,KAAmB,EAAQ;IACnD,KAAK,CAACY,eAAe,CAACZ,KAAK,CAAC;IAC5B,IAAI,CAACC,OAAO,CAACS,iBAAiB,CAACV,KAAK,CAACW,SAAS,CAAC;EACjD;AACF", "ignoreList": []}