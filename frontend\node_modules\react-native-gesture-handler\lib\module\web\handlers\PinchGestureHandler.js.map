{"version": 3, "names": ["State", "DEFAULT_TOUCH_SLOP", "Gesture<PERSON>andler", "ScaleGestureDetector", "PinchGestureHandler", "scale", "velocity", "startingSpan", "spanSlop", "scaleDetectorListener", "onScaleBegin", "detector", "currentSpan", "onScale", "prevScaleFactor", "calculateScaleFactor", "tracker", "trackedPointersCount", "delta", "<PERSON><PERSON><PERSON><PERSON>", "Math", "abs", "state", "BEGAN", "activate", "onScaleEnd", "_detector", "scaleGestureDetector", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "focalX", "focusX", "focalY", "focusY", "onPointerDown", "event", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "end", "fail", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "UNDETERMINED", "resetProgress", "begin", "force", "onReset"], "sourceRoot": "../../../../src", "sources": ["web/handlers/PinchGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,kBAAkB,QAAQ,cAAc;AAGjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,oBAAoB,MAEpB,mCAAmC;AAE1C,eAAe,MAAMC,mBAAmB,SAASF,cAAc,CAAC;EACtDG,KAAK,GAAG,CAAC;EACTC,QAAQ,GAAG,CAAC;EAEZC,YAAY,GAAG,CAAC;EAChBC,QAAQ,GAAGP,kBAAkB;EAE7BQ,qBAAqB,GAAyB;IACpDC,YAAY,EAAGC,QAA8B,IAAc;MACzD,IAAI,CAACJ,YAAY,GAAGI,QAAQ,CAACC,WAAW;MACxC,OAAO,IAAI;IACb,CAAC;IACDC,OAAO,EAAGF,QAA8B,IAAc;MACpD,MAAMG,eAAuB,GAAG,IAAI,CAACT,KAAK;MAC1C,IAAI,CAACA,KAAK,IAAIM,QAAQ,CAACI,oBAAoB,CACzC,IAAI,CAACC,OAAO,CAACC,oBACf,CAAC;MAED,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,SAAS;MAChC,IAAID,KAAK,GAAG,CAAC,EAAE;QACb,IAAI,CAACZ,QAAQ,GAAG,CAAC,IAAI,CAACD,KAAK,GAAGS,eAAe,IAAII,KAAK;MACxD;MAEA,IACEE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACd,YAAY,GAAGI,QAAQ,CAACC,WAAW,CAAC,IAAI,IAAI,CAACJ,QAAQ,IACnE,IAAI,CAACc,KAAK,KAAKtB,KAAK,CAACuB,KAAK,EAC1B;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB;MACA,OAAO,IAAI;IACb,CAAC;IACDC,UAAU,EACRC,SAA+B,IAEtB,CAAC;EACd,CAAC;EAEOC,oBAAoB,GAAyB,IAAIxB,oBAAoB,CAC3E,IAAI,CAACM,qBACP,CAAC;EAEMmB,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAE;IAC3D,KAAK,CAACF,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;IAEzB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACtC;EAEUC,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACLC,MAAM,EAAE,IAAI,CAACN,oBAAoB,CAACO,MAAM;MACxCC,MAAM,EAAE,IAAI,CAACR,oBAAoB,CAACS,MAAM;MACxC9B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBD,KAAK,EAAE,IAAI,CAACA;IACd,CAAC;EACH;EAEUgC,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAACtB,OAAO,CAACuB,YAAY,CAACD,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACE,mBAAmB,CAACF,KAAK,CAAC;EACjC;EAEUG,YAAYA,CAACH,KAAmB,EAAQ;IAChD,IAAI,CAACtB,OAAO,CAACuB,YAAY,CAACD,KAAK,CAAC;IAChC,KAAK,CAACG,YAAY,CAACH,KAAK,CAAC;IACzB,IAAI,CAACI,QAAQ,CAAC,CAAC;IACf,IAAI,CAACf,oBAAoB,CAACgB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;EAC7D;EAEU4B,WAAWA,CAACN,KAAmB,EAAQ;IAC/C,KAAK,CAACM,WAAW,CAACN,KAAK,CAAC;IACxB,IAAI,CAACtB,OAAO,CAAC6B,iBAAiB,CAACP,KAAK,CAACQ,SAAS,CAAC;IAC/C,IAAI,IAAI,CAACxB,KAAK,KAAKtB,KAAK,CAAC+C,MAAM,EAAE;MAC/B;IACF;IACA,IAAI,CAACpB,oBAAoB,CAACgB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;IAE3D,IAAI,IAAI,CAACM,KAAK,KAAKtB,KAAK,CAAC+C,MAAM,EAAE;MAC/B,IAAI,CAACC,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACC,IAAI,CAAC,CAAC;IACb;EACF;EAEUC,eAAeA,CAACZ,KAAmB,EAAQ;IACnD,KAAK,CAACY,eAAe,CAACZ,KAAK,CAAC;IAC5B,IAAI,CAACX,oBAAoB,CAACgB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;IAC3D,IAAI,CAACA,OAAO,CAAC6B,iBAAiB,CAACP,KAAK,CAACQ,SAAS,CAAC;IAE/C,IAAI,IAAI,CAACxB,KAAK,KAAKtB,KAAK,CAAC+C,MAAM,IAAI,IAAI,CAAC/B,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACxE,IAAI,CAAC+B,GAAG,CAAC,CAAC;IACZ;EACF;EAEUG,aAAaA,CAACb,KAAmB,EAAQ;IACjD,IAAI,IAAI,CAACtB,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IACA,IAAI,CAACD,OAAO,CAACoC,KAAK,CAACd,KAAK,CAAC;IAEzB,IAAI,CAACX,oBAAoB,CAACgB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;IAC3D,KAAK,CAACmC,aAAa,CAACb,KAAK,CAAC;EAC5B;EACUe,oBAAoBA,CAACf,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAACtB,OAAO,CAACC,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IACA,IAAI,CAACD,OAAO,CAACoC,KAAK,CAACd,KAAK,CAAC;IAEzB,IAAI,CAACX,oBAAoB,CAACgB,YAAY,CAACL,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;IAC3D,KAAK,CAACqC,oBAAoB,CAACf,KAAK,CAAC;EACnC;EAEQI,QAAQA,CAAA,EAAS;IACvB,IAAI,IAAI,CAACpB,KAAK,KAAKtB,KAAK,CAACsD,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEOhC,QAAQA,CAACiC,KAAe,EAAQ;IACrC,IAAI,IAAI,CAACnC,KAAK,KAAKtB,KAAK,CAAC+C,MAAM,EAAE;MAC/B,IAAI,CAACQ,aAAa,CAAC,CAAC;IACtB;IAEA,KAAK,CAAC/B,QAAQ,CAACiC,KAAK,CAAC;EACvB;EAEUC,OAAOA,CAAA,EAAS;IACxB,IAAI,CAACH,aAAa,CAAC,CAAC;EACtB;EAEUA,aAAaA,CAAA,EAAS;IAC9B,IAAI,IAAI,CAACjC,KAAK,KAAKtB,KAAK,CAAC+C,MAAM,EAAE;MAC/B;IACF;IACA,IAAI,CAACzC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACD,KAAK,GAAG,CAAC;EAChB;AACF", "ignoreList": []}