{"version": 3, "names": ["State", "Gesture<PERSON>andler", "RotationGestureDetector", "ROTATION_RECOGNITION_THRESHOLD", "Math", "PI", "RotationGestureHandler", "rotation", "velocity", "cachedAnchorX", "cachedAnchorY", "rotationGestureListener", "onRotationBegin", "_detector", "onRotation", "detector", "previousRotation", "delta", "<PERSON><PERSON><PERSON><PERSON>", "abs", "state", "BEGAN", "activate", "onRotationEnd", "end", "rotationGestureDetector", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "anchorX", "getAnchorX", "anchorY", "getAnchorY", "onPointerDown", "event", "tracker", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerMove", "trackedPointersCount", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "fail", "onPointerRemove", "UNDETERMINED", "begin", "onReset", "reset"], "sourceRoot": "../../../../src", "sources": ["web/handlers/RotationGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AAGnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,uBAAuB,MAEvB,sCAAsC;AAE7C,MAAMC,8BAA8B,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE;AAEnD,eAAe,MAAMC,sBAAsB,SAASL,cAAc,CAAC;EACzDM,QAAQ,GAAG,CAAC;EACZC,QAAQ,GAAG,CAAC;EAEZC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC;EAEjBC,uBAAuB,GAA4B;IACzDC,eAAe,EAAGC,SAAkC,IAAc,IAAI;IACtEC,UAAU,EAAGC,QAAiC,IAAc;MAC1D,MAAMC,gBAAwB,GAAG,IAAI,CAACT,QAAQ;MAC9C,IAAI,CAACA,QAAQ,IAAIQ,QAAQ,CAACR,QAAQ;MAElC,MAAMU,KAAK,GAAGF,QAAQ,CAACG,SAAS;MAEhC,IAAID,KAAK,GAAG,CAAC,EAAE;QACb,IAAI,CAACT,QAAQ,GAAG,CAAC,IAAI,CAACD,QAAQ,GAAGS,gBAAgB,IAAIC,KAAK;MAC5D;MAEA,IACEb,IAAI,CAACe,GAAG,CAAC,IAAI,CAACZ,QAAQ,CAAC,IAAIJ,8BAA8B,IACzD,IAAI,CAACiB,KAAK,KAAKpB,KAAK,CAACqB,KAAK,EAC1B;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB;MAEA,OAAO,IAAI;IACb,CAAC;IACDC,aAAa,EAAGV,SAAkC,IAAW;MAC3D,IAAI,CAACW,GAAG,CAAC,CAAC;IACZ;EACF,CAAC;EAEOC,uBAAuB,GAC7B,IAAIvB,uBAAuB,CAAC,IAAI,CAACS,uBAAuB,CAAC;EAEpDe,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAQ;IACjE,KAAK,CAACF,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;IAEzB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACtC;EAEUC,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACLvB,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC;MAC3CwB,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;MAC1BC,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;MAC1B1B,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG;IAC5C,CAAC;EACH;EAEOwB,UAAUA,CAAA,EAAW;IAC1B,MAAMD,OAAO,GAAG,IAAI,CAACN,uBAAuB,CAACM,OAAO;IAEpD,OAAOA,OAAO,GAAGA,OAAO,GAAG,IAAI,CAACtB,aAAa;EAC/C;EAEOyB,UAAUA,CAAA,EAAW;IAC1B,MAAMD,OAAO,GAAG,IAAI,CAACR,uBAAuB,CAACQ,OAAO;IAEpD,OAAOA,OAAO,GAAGA,OAAO,GAAG,IAAI,CAACvB,aAAa;EAC/C;EAEUyB,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACG,mBAAmB,CAACH,KAAK,CAAC;EACjC;EAEUI,YAAYA,CAACJ,KAAmB,EAAQ;IAChD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACI,YAAY,CAACJ,KAAK,CAAC;IAEzB,IAAI,CAACK,QAAQ,CAAC,CAAC;IACf,IAAI,CAAChB,uBAAuB,CAACiB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;EAChE;EAEUM,aAAaA,CAACP,KAAmB,EAAQ;IACjD,IAAI,IAAI,CAACC,OAAO,CAACO,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IAEA,IAAI,IAAI,CAACZ,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACvB,aAAa,GAAG,IAAI,CAACuB,UAAU,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACwB,UAAU,CAAC,CAAC;IACxC;IAEA,IAAI,CAACG,OAAO,CAACQ,KAAK,CAACT,KAAK,CAAC;IAEzB,IAAI,CAACX,uBAAuB,CAACiB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,KAAK,CAACM,aAAa,CAACP,KAAK,CAAC;EAC5B;EAEUU,oBAAoBA,CAACV,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAACC,OAAO,CAACO,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IAEA,IAAI,IAAI,CAACZ,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACvB,aAAa,GAAG,IAAI,CAACuB,UAAU,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACwB,UAAU,CAAC,CAAC;IACxC;IAEA,IAAI,CAACG,OAAO,CAACQ,KAAK,CAACT,KAAK,CAAC;IAEzB,IAAI,CAACX,uBAAuB,CAACiB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,KAAK,CAACS,oBAAoB,CAACV,KAAK,CAAC;EACnC;EAEUW,WAAWA,CAACX,KAAmB,EAAQ;IAC/C,KAAK,CAACW,WAAW,CAACX,KAAK,CAAC;IACxB,IAAI,CAACC,OAAO,CAACW,iBAAiB,CAACZ,KAAK,CAACa,SAAS,CAAC;IAC/C,IAAI,CAACxB,uBAAuB,CAACiB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,IAAI,IAAI,CAACjB,KAAK,KAAKpB,KAAK,CAACkD,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,IAAI,CAAC9B,KAAK,KAAKpB,KAAK,CAACkD,MAAM,EAAE;MAC/B,IAAI,CAAC1B,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAAC2B,IAAI,CAAC,CAAC;IACb;EACF;EAEUC,eAAeA,CAAChB,KAAmB,EAAQ;IACnD,KAAK,CAACgB,eAAe,CAAChB,KAAK,CAAC;IAC5B,IAAI,CAACX,uBAAuB,CAACiB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAC9D,IAAI,CAACA,OAAO,CAACW,iBAAiB,CAACZ,KAAK,CAACa,SAAS,CAAC;EACjD;EAEUR,QAAQA,CAAA,EAAS;IACzB,IAAI,IAAI,CAACrB,KAAK,KAAKpB,KAAK,CAACqD,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEUC,OAAOA,CAAA,EAAS;IACxB,IAAI,IAAI,CAACnC,KAAK,KAAKpB,KAAK,CAACkD,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,CAAC3C,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACiB,uBAAuB,CAAC+B,KAAK,CAAC,CAAC;EACtC;AACF", "ignoreList": []}