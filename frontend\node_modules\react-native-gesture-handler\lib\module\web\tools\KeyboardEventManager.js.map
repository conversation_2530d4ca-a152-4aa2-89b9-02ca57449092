{"version": 3, "names": ["EventTypes", "EventManager", "PointerType", "KeyboardEventManager", "activationKeys", "cancelationKeys", "isPressed", "registeredStaticListeners", "instances", "Set", "keyUpStatic<PERSON><PERSON>back", "event", "indexOf", "key", "for<PERSON>ach", "item", "onKeyUp", "keyDownCallback", "dispatchEvent", "CANCEL", "DOWN", "UP", "eventType", "target", "HTMLElement", "adaptedEvent", "mapEvent", "onPointerUp", "onPointerDown", "onPointerCancel", "registerListeners", "view", "addEventListener", "add", "document", "capture", "unregisterListeners", "removeEventListener", "delete", "size", "viewRect", "getBoundingClientRect", "viewportPosition", "x", "width", "y", "height", "relativePosition", "offsetX", "offsetY", "pointerId", "pointerType", "KEY", "time", "timeStamp"], "sourceRoot": "../../../../src", "sources": ["web/tools/KeyboardEventManager.ts"], "mappings": ";;AAAA,SAAuBA,UAAU,QAAQ,eAAe;AACxD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,eAAe,MAAMC,oBAAoB,SAASF,YAAY,CAAc;EAC1E,OAAeG,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC;EAC9C,OAAeC,eAAe,GAAG,CAAC,KAAK,CAAC;EAChCC,SAAS,GAAG,KAAK;EACzB,OAAeC,yBAAyB,GAAG,KAAK;EAChD,OAAeC,SAAS,GAA8B,IAAIC,GAAG,CAAC,CAAC;EAE/D,OAAeC,mBAAmB,GAAIC,KAAoB,IAAW;IACnE;IACA;IACA;;IAEA,IAAI,IAAI,CAACP,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAACL,SAAS,CAACM,OAAO,CAAEC,IAAI,IAAK;MAC/BA,IAAI,CAACC,OAAO,CAACL,KAAK,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EAEOM,eAAe,GAAIN,KAAoB,IAAW;IACxD,IACER,oBAAoB,CAACE,eAAe,CAACO,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAC9D,IAAI,CAACP,SAAS,EACd;MACA,IAAI,CAACY,aAAa,CAACP,KAAK,EAAEX,UAAU,CAACmB,MAAM,CAAC;MAC5C;IACF;IAEA,IAAIhB,oBAAoB,CAACC,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjE;IACF;IAEA,IAAI,CAACK,aAAa,CAACP,KAAK,EAAEX,UAAU,CAACoB,IAAI,CAAC;EAC5C,CAAC;EAEOJ,OAAO,GAAIL,KAAoB,IAAW;IAChD,IACER,oBAAoB,CAACC,cAAc,CAACQ,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAC7D,CAAC,IAAI,CAACP,SAAS,EACf;MACA;IACF;IAEA,IAAI,CAACY,aAAa,CAACP,KAAK,EAAEX,UAAU,CAACqB,EAAE,CAAC;EAC1C,CAAC;EAEOH,aAAaA,CAACP,KAAoB,EAAEW,SAAqB,EAAE;IACjE,IAAI,EAAEX,KAAK,CAACY,MAAM,YAAYC,WAAW,CAAC,EAAE;MAC1C;IACF;IAEA,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACf,KAAK,EAAEW,SAAS,CAAC;IAEpD,QAAQA,SAAS;MACf,KAAKtB,UAAU,CAACqB,EAAE;QAChB,IAAI,CAACf,SAAS,GAAG,KAAK;QACtB,IAAI,CAACqB,WAAW,CAACF,YAAY,CAAC;QAC9B;MACF,KAAKzB,UAAU,CAACoB,IAAI;QAClB,IAAI,CAACd,SAAS,GAAG,IAAI;QACrB,IAAI,CAACsB,aAAa,CAACH,YAAY,CAAC;QAChC;MACF,KAAKzB,UAAU,CAACmB,MAAM;QACpB,IAAI,CAACb,SAAS,GAAG,KAAK;QACtB,IAAI,CAACuB,eAAe,CAACJ,YAAY,CAAC;QAClC;IACJ;EACF;EAEOK,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACf,eAAe,CAAC;IAE3Dd,oBAAoB,CAACK,SAAS,CAACyB,GAAG,CAAC,IAAI,CAAC;IAExC,IAAI,CAAC9B,oBAAoB,CAACI,yBAAyB,EAAE;MACnDJ,oBAAoB,CAACI,yBAAyB,GAAG,IAAI;MACrD2B,QAAQ,CAACF,gBAAgB,CACvB,OAAO,EACP7B,oBAAoB,CAACO,mBAAmB,EACxC;QAAEyB,OAAO,EAAE;MAAK,CAClB,CAAC;IACH;EACF;EAEOC,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACL,IAAI,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACpB,eAAe,CAAC;IAE9Dd,oBAAoB,CAACK,SAAS,CAAC8B,MAAM,CAAC,IAAI,CAAC;IAE3C,IAAInC,oBAAoB,CAACK,SAAS,CAAC+B,IAAI,KAAK,CAAC,EAAE;MAC7CL,QAAQ,CAACG,mBAAmB,CAC1B,OAAO,EACPlC,oBAAoB,CAACO,mBAAmB,EACxC;QAAEyB,OAAO,EAAE;MAAK,CAClB,CAAC;MACDhC,oBAAoB,CAACI,yBAAyB,GAAG,KAAK;IACxD;EACF;EAEUmB,QAAQA,CAChBf,KAAoB,EACpBW,SAAqB,EACP;IACd,MAAMkB,QAAQ,GAAI7B,KAAK,CAACY,MAAM,CAAiBkB,qBAAqB,CAAC,CAAC;IAEtE,MAAMC,gBAAgB,GAAG;MACvBC,CAAC,EAAEH,QAAQ,EAAEG,CAAC,GAAGH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACpCC,CAAC,EAAEL,QAAQ,EAAEK,CAAC,GAAGL,QAAQ,EAAEM,MAAM,GAAG;IACtC,CAAC;IAED,MAAMC,gBAAgB,GAAG;MACvBJ,CAAC,EAAEH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACtBC,CAAC,EAAEL,QAAQ,EAAEM,MAAM,GAAG;IACxB,CAAC;IAED,OAAO;MACLH,CAAC,EAAED,gBAAgB,CAACC,CAAC;MACrBE,CAAC,EAAEH,gBAAgB,CAACG,CAAC;MACrBG,OAAO,EAAED,gBAAgB,CAACJ,CAAC;MAC3BM,OAAO,EAAEF,gBAAgB,CAACF,CAAC;MAC3BK,SAAS,EAAE,CAAC;MACZ5B,SAAS,EAAEA,SAAS;MACpB6B,WAAW,EAAEjD,WAAW,CAACkD,GAAG;MAC5BC,IAAI,EAAE1C,KAAK,CAAC2C;IACd,CAAC;EACH;AACF", "ignoreList": []}