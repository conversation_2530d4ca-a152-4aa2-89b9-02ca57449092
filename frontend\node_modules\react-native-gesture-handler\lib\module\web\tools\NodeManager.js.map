{"version": 3, "names": ["NodeManager", "gestures", "<PERSON><PERSON><PERSON><PERSON>", "tag", "Error", "createGestureHandler", "handlerTag", "handler", "dropGestureHandler", "onDestroy", "nodes"], "sourceRoot": "../../../../src", "sources": ["web/tools/NodeManager.ts"], "mappings": ";;AAIA;AACA,eAAe,MAAeA,WAAW,CAAC;EACxC,OAAeC,QAAQ,GAGnB,CAAC,CAAC;EAEN,OAAcC,UAAUA,CAACC,GAAW,EAAmB;IACrD,IAAIA,GAAG,IAAI,IAAI,CAACF,QAAQ,EAAE;MACxB,OAAO,IAAI,CAACA,QAAQ,CAACE,GAAG,CAAC;IAC3B;IAEA,MAAM,IAAIC,KAAK,CAAC,sBAAsBD,GAAG,EAAE,CAAC;EAC9C;EAEA,OAAcE,oBAAoBA,CAChCC,UAAkB,EAClBC,OAA+C,EACzC;IACN,IAAID,UAAU,IAAI,IAAI,CAACL,QAAQ,EAAE;MAC/B,MAAM,IAAIG,KAAK,CACb,oBAAoBE,UAAU,mGAChC,CAAC;IACH;IAEA,IAAI,CAACL,QAAQ,CAACK,UAAU,CAAC,GAAGC,OAAO;IACnC,IAAI,CAACN,QAAQ,CAACK,UAAU,CAAC,CAACA,UAAU,GAAGA,UAAU;EACnD;EAEA,OAAcE,kBAAkBA,CAACF,UAAkB,EAAQ;IACzD,IAAI,EAAEA,UAAU,IAAI,IAAI,CAACL,QAAQ,CAAC,EAAE;MAClC;IACF;IAEA,IAAI,CAACA,QAAQ,CAACK,UAAU,CAAC,CAACG,SAAS,CAAC,CAAC;;IAErC;IACA,OAAO,IAAI,CAACR,QAAQ,CAACK,UAAU,CAAC;EAClC;EAEA,WAAkBI,KAAKA,CAAA,EAAG;IACxB,OAAO;MAAE,GAAG,IAAI,CAACT;IAAS,CAAC;EAC7B;AACF", "ignoreList": []}