{"version": 3, "names": ["Hammer", "State", "PressGestureHandler", "isnan", "isValidNumber", "LongPressGestureHandler", "minDurationMs", "config", "maxDist", "updateHasCustomActivationCriteria", "maxDistSq", "getConfig", "hasCustomActivationCriteria", "shouldCancelWhenOutside", "getHammerConfig", "time", "getState", "type", "INPUT_START", "ACTIVE", "INPUT_MOVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED"], "sourceRoot": "../../../src", "sources": ["web_hammer/LongPressGestureHandler.ts"], "mappings": ";;AAAA;AACA;AACA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAASC,KAAK,QAAQ,UAAU;AAChC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,KAAK,EAAEC,aAAa,QAAQ,SAAS;AAI9C,MAAMC,uBAAuB,SAASH,mBAAmB,CAAC;EACxD,IAAII,aAAaA,CAAA,EAAW;IAC1B;IACA,OAAOH,KAAK,CAAC,IAAI,CAACI,MAAM,CAACD,aAAa,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,MAAM,CAACD,aAAa;EAC3E;EAEA,IAAIE,OAAOA,CAAA,EAAG;IACZ;IACA,OAAOL,KAAK,CAAC,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;EAC7D;EAEAC,iCAAiCA,CAAC;IAAEC;EAAkB,CAAC,EAAE;IACvD,OAAO,CAACN,aAAa,CAACM,SAAS,CAAC;EAClC;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLC,uBAAuB,EAAE,IAAI;QAC7BH,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACH,MAAM;EACpB;EAEAO,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1B;MACAC,IAAI,EAAE,IAAI,CAACT;IACb,CAAC;EACH;EAEAU,QAAQA,CAACC,IAAmC,EAAE;IAC5C,OAAO;MACL,CAACjB,MAAM,CAACkB,WAAW,GAAGjB,KAAK,CAACkB,MAAM;MAClC,CAACnB,MAAM,CAACoB,UAAU,GAAGnB,KAAK,CAACkB,MAAM;MACjC,CAACnB,MAAM,CAACqB,SAAS,GAAGpB,KAAK,CAACqB,GAAG;MAC7B,CAACtB,MAAM,CAACuB,YAAY,GAAGtB,KAAK,CAACuB;IAC/B,CAAC,CAACP,IAAI,CAAC;EACT;AACF;AAEA,eAAeZ,uBAAuB", "ignoreList": []}