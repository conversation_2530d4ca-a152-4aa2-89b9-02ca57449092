{"version": 3, "names": ["Hammer", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "DraggingGestureHandler", "isValidNumber", "isnan", "TEST_MIN_IF_NOT_NAN", "VEC_LEN_SQ", "State", "PanGestureHandler", "name", "NativeGestureClass", "Pan", "getHammerConfig", "direction", "getDirection", "getState", "type", "nextState", "previousState", "UNDETERMINED", "ACTIVE", "BEGAN", "config", "getConfig", "activeOffsetXStart", "activeOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "minDist", "directions", "horizontalDirections", "DIRECTION_ALL", "push", "DIRECTION_LEFT", "DIRECTION_RIGHT", "length", "DIRECTION_HORIZONTAL", "concat", "verticalDirections", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_VERTICAL", "DIRECTION_NONE", "hasCustomActivationCriteria", "minDistSq", "shouldFailUnderCustomCriteria", "deltaX", "deltaY", "criteria", "failOffsetXStart", "failOffsetXEnd", "failOffsetYStart", "failOffsetYEnd", "shouldActivateUnderCustomCriteria", "velocity", "x", "y", "minVelocityX", "minVelocityY", "minVelocitySq", "shouldMultiFingerPanFail", "pointer<PERSON><PERSON><PERSON>", "scale", "deltaRotation", "deltaScale", "Math", "abs", "absDeltaRotation", "updateHasCustomActivationCriteria", "isGestureEnabledForEvent", "props", "_recognizer", "inputData", "failed", "velocityX", "velocityY", "maxPointers", "success"], "sourceRoot": "../../../src", "sources": ["web_hammer/PanGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAEEC,oCAAoC,EACpCC,uCAAuC,QAClC,aAAa;AACpB,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,aAAa,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,SAAS;AAC/E,SAASC,KAAK,QAAQ,UAAU;AAGhC,MAAMC,iBAAiB,SAASN,sBAAsB,CAAC;EACrD,IAAIO,IAAIA,CAAA,EAAG;IACT,OAAO,KAAK;EACd;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOX,MAAM,CAACY,GAAG;EACnB;EAEAC,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1BC,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC;IAC/B,CAAC;EACH;EAEAC,QAAQA,CAACC,IAA2B,EAAE;IACpC,MAAMC,SAAS,GAAG,KAAK,CAACF,QAAQ,CAACC,IAAI,CAAC;IACtC;IACA,IACE,IAAI,CAACE,aAAa,KAAKX,KAAK,CAACY,YAAY,IACzCF,SAAS,KAAKV,KAAK,CAACa,MAAM,EAC1B;MACA,OAAOb,KAAK,CAACc,KAAK;IACpB;IACA,OAAOJ,SAAS;EAClB;EAEAH,YAAYA,CAAA,EAAG;IACb,MAAMQ,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC/B,MAAM;MACJC,kBAAkB;MAClBC,gBAAgB;MAChBC,kBAAkB;MAClBC,gBAAgB;MAChBC;IACF,CAAC,GAAGN,MAAM;IACV,IAAIO,UAAoB,GAAG,EAAE;IAC7B,IAAIC,oBAAoB,GAAG,EAAE;IAE7B,IAAI,CAAC1B,KAAK,CAACwB,OAAO,CAAC,EAAE;MACnB,OAAO7B,MAAM,CAACgC,aAAa;IAC7B;IAEA,IAAI,CAAC3B,KAAK,CAACoB,kBAAkB,CAAC,EAAE;MAC9BM,oBAAoB,CAACE,IAAI,CAACjC,MAAM,CAACkC,cAAc,CAAC;IAClD;IACA,IAAI,CAAC7B,KAAK,CAACqB,gBAAgB,CAAC,EAAE;MAC5BK,oBAAoB,CAACE,IAAI,CAACjC,MAAM,CAACmC,eAAe,CAAC;IACnD;IACA,IAAIJ,oBAAoB,CAACK,MAAM,KAAK,CAAC,EAAE;MACrCL,oBAAoB,GAAG,CAAC/B,MAAM,CAACqC,oBAAoB,CAAC;IACtD;IAEAP,UAAU,GAAGA,UAAU,CAACQ,MAAM,CAACP,oBAAoB,CAAC;IACpD,IAAIQ,kBAAkB,GAAG,EAAE;IAE3B,IAAI,CAAClC,KAAK,CAACsB,kBAAkB,CAAC,EAAE;MAC9BY,kBAAkB,CAACN,IAAI,CAACjC,MAAM,CAACwC,YAAY,CAAC;IAC9C;IACA,IAAI,CAACnC,KAAK,CAACuB,gBAAgB,CAAC,EAAE;MAC5BW,kBAAkB,CAACN,IAAI,CAACjC,MAAM,CAACyC,cAAc,CAAC;IAChD;IAEA,IAAIF,kBAAkB,CAACH,MAAM,KAAK,CAAC,EAAE;MACnCG,kBAAkB,GAAG,CAACvC,MAAM,CAAC0C,kBAAkB,CAAC;IAClD;IAEAZ,UAAU,GAAGA,UAAU,CAACQ,MAAM,CAACC,kBAAkB,CAAC;IAElD,IAAI,CAACT,UAAU,CAACM,MAAM,EAAE;MACtB,OAAOpC,MAAM,CAAC2C,cAAc;IAC9B;IACA,IACEb,UAAU,CAAC,CAAC,CAAC,KAAK9B,MAAM,CAACqC,oBAAoB,IAC7CP,UAAU,CAAC,CAAC,CAAC,KAAK9B,MAAM,CAAC0C,kBAAkB,EAC3C;MACA,OAAO1C,MAAM,CAACgC,aAAa;IAC7B;IACA,IAAID,oBAAoB,CAACK,MAAM,IAAIG,kBAAkB,CAACH,MAAM,EAAE;MAC5D,OAAOpC,MAAM,CAACgC,aAAa;IAC7B;IAEA,OAAOF,UAAU,CAAC,CAAC,CAAC;EACtB;EAEAN,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACoB,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLC,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACtB,MAAM;EACpB;EAEAuB,6BAA6BA,CAC3B;IAAEC,MAAM;IAAEC;EAAuB,CAAC,EAClCC,QAAa,EACb;IACA,OACG,CAAC5C,KAAK,CAAC4C,QAAQ,CAACC,gBAAgB,CAAC,IAChCH,MAAM,GAAGE,QAAQ,CAACC,gBAAgB,IACnC,CAAC7C,KAAK,CAAC4C,QAAQ,CAACE,cAAc,CAAC,IAAIJ,MAAM,GAAGE,QAAQ,CAACE,cAAe,IACpE,CAAC9C,KAAK,CAAC4C,QAAQ,CAACG,gBAAgB,CAAC,IAChCJ,MAAM,GAAGC,QAAQ,CAACG,gBAAiB,IACpC,CAAC/C,KAAK,CAAC4C,QAAQ,CAACI,cAAc,CAAC,IAAIL,MAAM,GAAGC,QAAQ,CAACI,cAAe;EAEzE;EAEAC,iCAAiCA,CAC/B;IAAEP,MAAM;IAAEC,MAAM;IAAEO;EAAc,CAAC,EACjCN,QAAa,EACb;IACA,OACG,CAAC5C,KAAK,CAAC4C,QAAQ,CAACxB,kBAAkB,CAAC,IAClCsB,MAAM,GAAGE,QAAQ,CAACxB,kBAAkB,IACrC,CAACpB,KAAK,CAAC4C,QAAQ,CAACvB,gBAAgB,CAAC,IAChCqB,MAAM,GAAGE,QAAQ,CAACvB,gBAAiB,IACpC,CAACrB,KAAK,CAAC4C,QAAQ,CAACtB,kBAAkB,CAAC,IAClCqB,MAAM,GAAGC,QAAQ,CAACtB,kBAAmB,IACtC,CAACtB,KAAK,CAAC4C,QAAQ,CAACrB,gBAAgB,CAAC,IAChCoB,MAAM,GAAGC,QAAQ,CAACrB,gBAAiB,IACrCtB,mBAAmB,CACjBC,UAAU,CAAC;MAAEiD,CAAC,EAAET,MAAM;MAAEU,CAAC,EAAET;IAAO,CAAC,CAAC,EACpCC,QAAQ,CAACJ,SACX,CAAC,IACDvC,mBAAmB,CAACiD,QAAQ,CAACC,CAAC,EAAEP,QAAQ,CAACS,YAAY,CAAC,IACtDpD,mBAAmB,CAACiD,QAAQ,CAACE,CAAC,EAAER,QAAQ,CAACU,YAAY,CAAC,IACtDrD,mBAAmB,CAACC,UAAU,CAACgD,QAAQ,CAAC,EAAEN,QAAQ,CAACW,aAAa,CAAC;EAErE;EAEAC,wBAAwBA,CAAC;IACvBC,aAAa;IACbC,KAAK;IACLC;EAKF,CAAC,EAAE;IACD,IAAIF,aAAa,IAAI,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,GAAG,CAAC,CAAC;IACtC,MAAMK,gBAAgB,GAAGF,IAAI,CAACC,GAAG,CAACH,aAAa,CAAC;IAChD,IAAIC,UAAU,GAAGhE,oCAAoC,EAAE;MACrD;MACA;MACA,OAAO,IAAI;IACb;IACA,IAAImE,gBAAgB,GAAGlE,uCAAuC,EAAE;MAC9D;MACA;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAmE,iCAAiCA,CAC/BpB,QAAmE,EACnE;IACA,OACE7C,aAAa,CAAC6C,QAAQ,CAACJ,SAAS,CAAC,IACjCzC,aAAa,CAAC6C,QAAQ,CAACS,YAAY,CAAC,IACpCtD,aAAa,CAAC6C,QAAQ,CAACU,YAAY,CAAC,IACpCvD,aAAa,CAAC6C,QAAQ,CAACW,aAAa,CAAC,IACrCxD,aAAa,CAAC6C,QAAQ,CAACxB,kBAAkB,CAAC,IAC1CrB,aAAa,CAAC6C,QAAQ,CAACvB,gBAAgB,CAAC,IACxCtB,aAAa,CAAC6C,QAAQ,CAACtB,kBAAkB,CAAC,IAC1CvB,aAAa,CAAC6C,QAAQ,CAACrB,gBAAgB,CAAC;EAE5C;EAEA0C,wBAAwBA,CACtBC,KAAU,EACVC,WAAgB,EAChBC,SAAqD,EACrD;IACA,IAAI,IAAI,CAAC3B,6BAA6B,CAAC2B,SAAS,EAAEF,KAAK,CAAC,EAAE;MACxD,OAAO;QAAEG,MAAM,EAAE;MAAK,CAAC;IACzB;IAEA,MAAMnB,QAAQ,GAAG;MAAEC,CAAC,EAAEiB,SAAS,CAACE,SAAS;MAAElB,CAAC,EAAEgB,SAAS,CAACG;IAAU,CAAC;IACnE,IACE,IAAI,CAAChC,2BAA2B,IAChC,IAAI,CAACU,iCAAiC,CACpC;MAAEP,MAAM,EAAE0B,SAAS,CAAC1B,MAAM;MAAEC,MAAM,EAAEyB,SAAS,CAACzB,MAAM;MAAEO;IAAS,CAAC,EAChEgB,KACF,CAAC,EACD;MACA,IACE,IAAI,CAACV,wBAAwB,CAAC;QAC5BC,aAAa,EAAEW,SAAS,CAACI,WAAW;QACpCd,KAAK,EAAEU,SAAS,CAACV,KAAK;QACtBC,aAAa,EAAES,SAAS,CAACT;MAC3B,CAAC,CAAC,EACF;QACA,OAAO;UACLU,MAAM,EAAE;QACV,CAAC;MACH;MACA,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B;IACA,OAAO;MAAEA,OAAO,EAAE;IAAM,CAAC;EAC3B;AACF;AAEA,eAAerE,iBAAiB", "ignoreList": []}