{"version": 3, "names": ["Hammer", "IndiscreteGestureHandler", "PinchGestureHandler", "name", "NativeGestureClass", "Pinch", "transformNativeEvent", "scale", "velocity", "center", "focalX", "x", "focalY", "y"], "sourceRoot": "../../../src", "sources": ["web_hammer/PinchGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,gBAAgB;AAGnC,OAAOC,wBAAwB,MAAM,4BAA4B;AAEjE,MAAMC,mBAAmB,SAASD,wBAAwB,CAAC;EACzD,IAAIE,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAC,oBAAoBA,CAAC;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAuB,CAAC,EAAE;IAChE,OAAO;MACLC,MAAM,EAAED,MAAM,CAACE,CAAC;MAChBC,MAAM,EAAEH,MAAM,CAACI,CAAC;MAChBL,QAAQ;MACRD;IACF,CAAC;EACH;AACF;AAEA,eAAeL,mBAAmB", "ignoreList": []}