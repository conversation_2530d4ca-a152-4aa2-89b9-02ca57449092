{"version": 3, "names": ["Hammer", "State", "CONTENT_TOUCHES_DELAY", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "DiscreteGestureHandler", "fireAfterInterval", "isValidNumber", "isnan", "PressGestureHandler", "initialEvent", "name", "minDurationMs", "config", "maxDist", "NativeGestureClass", "Press", "shouldDelayTouches", "simulateCancelEvent", "inputData", "hasGestureFailed", "cancelEvent", "updateHasCustomActivationCriteria", "shouldCancelWhenOutside", "maxDistSq", "getState", "type", "INPUT_START", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "CANCELLED", "getConfig", "hasCustomActivationCriteria", "getHammerConfig", "time", "onGestureActivated", "ev", "onGestureStart", "shouldDelayTouchForEvent", "pointerType", "isGestureRunning", "clearTimeout", "visualFeedbackTimer", "sendGestureStartedEvent", "sendEvent", "eventType", "<PERSON><PERSON><PERSON><PERSON>", "forceInvalidate", "event", "onRawEvent", "isFinal", "timeout", "onGestureEnded", "updateGestureConfig", "shouldActivateOnStart", "disallowInterruption", "Number", "NaN", "minPointers", "maxPointers", "props"], "sourceRoot": "../../../src", "sources": ["web_hammer/PressGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAASC,KAAK,QAAQ,UAAU;AAChC,SACEC,qBAAqB,EACrBC,mCAAmC,QAE9B,aAAa;AACpB,OAAOC,sBAAsB,MAAM,0BAA0B;AAE7D,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,KAAK,QAAQ,SAAS;AAEjE,MAAMC,mBAAmB,SAASJ,sBAAsB,CAAC;EAE/CK,YAAY,GAA0B,IAAI;EAClD,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,aAAaA,CAAA,EAAG;IAClB;IACA,OAAOJ,KAAK,CAAC,IAAI,CAACK,MAAM,CAACD,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACC,MAAM,CAACD,aAAa;EACzE;EAEA,IAAIE,OAAOA,CAAA,EAAG;IACZ,OAAON,KAAK,CAAC,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;EAC7D;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOd,MAAM,CAACe,KAAK;EACrB;EAEAC,kBAAkB,GAAG,IAAI;EAEzBC,mBAAmBA,CAACC,SAAyB,EAAE;IAC7C;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,CAACF,SAAS,CAAC;EAC7B;EAEAG,iCAAiCA,CAAC;IAChCC,uBAAuB;IACvBC;EAC6C,CAAC,EAAE;IAChD,OAAOD,uBAAuB,IAAI,CAAChB,aAAa,CAACiB,SAAS,CAAC;EAC7D;EAEAC,QAAQA,CAACC,IAAmC,EAAS;IACnD,OAAO;MACL,CAACzB,MAAM,CAAC0B,WAAW,GAAGzB,KAAK,CAAC0B,KAAK;MACjC,CAAC3B,MAAM,CAAC4B,UAAU,GAAG3B,KAAK,CAAC4B,MAAM;MACjC,CAAC7B,MAAM,CAAC8B,SAAS,GAAG7B,KAAK,CAAC8B,GAAG;MAC7B,CAAC/B,MAAM,CAACgC,YAAY,GAAG/B,KAAK,CAACgC;IAC/B,CAAC,CAACR,IAAI,CAAC;EACT;EAEAS,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLb,uBAAuB,EAAE,IAAI;QAC7BC,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACX,MAAM;EACpB;EAEAwB,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1B;MACAC,IAAI,EAAE,IAAI,CAAC1B;IACb,CAAC;EACH;EAEA2B,kBAAkBA,CAACC,EAAkB,EAAE;IACrC,IAAI,CAACC,cAAc,CAACD,EAAE,CAAC;EACzB;EAEAE,wBAAwBA,CAAC;IAAEC;EAA4B,CAAC,EAAE;IACxD;IACA,OAAO,IAAI,CAAC1B,kBAAkB,IAAI0B,WAAW,KAAK,OAAO;EAC3D;EAEAF,cAAcA,CAACD,EAAkB,EAAE;IACjC,IAAI,CAACI,gBAAgB,GAAG,IAAI;IAC5BC,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACpC,YAAY,GAAG8B,EAAE;IACtB,IAAI,CAACM,mBAAmB,GAAGxC,iBAAiB,CAC1C,MAAM;MACJ,IAAI,CAACyC,uBAAuB,CAAC,IAAI,CAACrC,YAA8B,CAAC;MACjE,IAAI,CAACA,YAAY,GAAG,IAAI;IAC1B,CAAC,EACD,IAAI,CAACgC,wBAAwB,CAACF,EAAE,CAAC,IAAIrC,qBACvC,CAAC;EACH;EAEA4C,uBAAuBA,CAACP,EAAkB,EAAE;IAC1CK,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,SAAS,CAAC;MACb,GAAGR,EAAE;MACLS,SAAS,EAAEhD,MAAM,CAAC4B,UAAU;MAC5BqB,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAACC,KAAqB,EAAE;IACrC,KAAK,CAACD,eAAe,CAACC,KAAK,CAAC;IAC5BP,YAAY,CAAC,IAAI,CAACC,mBAAmB,CAAC;IACtC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACpC,YAAY,GAAG,IAAI;EAC1B;EAEA2C,UAAUA,CAACb,EAAkB,EAAE;IAC7B,KAAK,CAACa,UAAU,CAACb,EAAE,CAAC;IACpB,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAIJ,EAAE,CAACc,OAAO,EAAE;QACd,IAAIC,OAAO;QACX,IAAI,IAAI,CAACT,mBAAmB,EAAE;UAC5B;UACA;UACA;UACAS,OAAO,GAAGnD,mCAAmC;UAC7C,IAAI,CAAC2C,uBAAuB,CAAC,IAAI,CAACrC,YAA8B,CAAC;UACjE,IAAI,CAACA,YAAY,GAAG,IAAI;QAC1B;QACAJ,iBAAiB,CAAC,MAAM;UACtB,IAAI,CAAC0C,SAAS,CAAC;YACb,GAAGR,EAAE;YACLS,SAAS,EAAEhD,MAAM,CAAC8B,SAAS;YAC3BuB,OAAO,EAAE;UACX,CAAC,CAAC;UACF;UACA,IAAI,CAACE,cAAc,CAAC,CAAC;QACvB,CAAC,EAAED,OAAO,CAAC;MACb,CAAC,MAAM;QACL,IAAI,CAACP,SAAS,CAAC;UACb,GAAGR,EAAE;UACLS,SAAS,EAAEhD,MAAM,CAAC4B,UAAU;UAC5ByB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF;EAEAG,mBAAmBA,CAAC;IAClBC,qBAAqB,GAAG,KAAK;IAC7BC,oBAAoB,GAAG,KAAK;IAC5BpC,uBAAuB,GAAG,IAAI;IAC9BX,aAAa,GAAGgD,MAAM,CAACC,GAAG;IAC1B/C,OAAO,GAAG8C,MAAM,CAACC,GAAG;IACpBC,WAAW,GAAG,CAAC;IACfC,WAAW,GAAG,CAAC;IACf,GAAGC;EACL,CAAC,EAAE;IACD,OAAO,KAAK,CAACP,mBAAmB,CAAC;MAC/BC,qBAAqB;MACrBC,oBAAoB;MACpBpC,uBAAuB;MACvBX,aAAa;MACbE,OAAO;MACPgD,WAAW;MACXC,WAAW;MACX,GAAGC;IACL,CAAC,CAAC;EACJ;AACF;AACA,eAAevD,mBAAmB", "ignoreList": []}