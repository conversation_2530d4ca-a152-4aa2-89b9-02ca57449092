{"version": 3, "names": ["isnan", "v", "Number", "isNaN", "isValidNumber", "TEST_MIN_IF_NOT_NAN", "value", "limit", "VEC_LEN_SQ", "x", "y", "TEST_MAX_IF_NOT_NAN", "max", "fireAfterInterval", "method", "interval", "setTimeout"], "sourceRoot": "../../../src", "sources": ["web_hammer/utils.ts"], "mappings": ";;AAAA;AACA,OAAO,MAAMA,KAAK,GAAIC,CAAU,IAAKC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC;;AAEpD;AACA,OAAO,MAAMG,aAAa,GAAIH,CAAU,IACtC,OAAOA,CAAC,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC;AAE3C,OAAO,MAAMI,mBAAmB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAC9D,CAACP,KAAK,CAACO,KAAK,CAAC,KACXA,KAAK,GAAG,CAAC,IAAID,KAAK,IAAIC,KAAK,IAAMA,KAAK,IAAI,CAAC,IAAID,KAAK,IAAIC,KAAM,CAAC;AACnE,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG;AAAE,CAAC,GAAG,CAAC,CAAC,KAAKD,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;AAClE,OAAO,MAAMC,mBAAmB,GAAGA,CAACL,KAAa,EAAEM,GAAW,KAC5D,CAACZ,KAAK,CAACY,GAAG,CAAC,KAAMA,GAAG,GAAG,CAAC,IAAIN,KAAK,GAAGM,GAAG,IAAMA,GAAG,IAAI,CAAC,IAAIN,KAAK,GAAGM,GAAI,CAAC;AAExE,OAAO,SAASC,iBAAiBA,CAC/BC,MAAkB,EAClBC,QAA2B,EAC3B;EACA,IAAI,CAACA,QAAQ,EAAE;IACbD,MAAM,CAAC,CAAC;IACR,OAAO,IAAI;EACb;EACA,OAAOE,UAAU,CAAC,MAAMF,MAAM,CAAC,CAAC,EAAEC,QAAkB,CAAC;AACvD", "ignoreList": []}