export declare const CONTENT_TOUCHES_DELAY = 240;
export declare const CONTENT_TOUCHES_QUICK_TAP_END_DELAY = 50;
export declare const MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = 0.1;
export declare const MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = 7;
export declare const DEG_RAD: number;
export declare const EventMap: {
    readonly 1: 2;
    readonly 2: 4;
    readonly 4: 5;
    readonly 8: 1;
};
export declare const Direction: {
    RIGHT: number;
    LEFT: number;
    UP: number;
    DOWN: number;
};
export declare const DirectionMap: {
    4: number;
    2: number;
    8: number;
    16: number;
};
export declare const HammerInputNames: {
    1: string;
    2: string;
    4: string;
    8: string;
};
export declare const HammerDirectionNames: {
    6: string;
    8: string;
    16: string;
    24: string;
    1: string;
    30: string;
    4: string;
    2: string;
};
//# sourceMappingURL=constants.d.ts.map