{"name": "frontend", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@react-navigation/stack": "^7.4.8", "@tanstack/react-query": "^5.90.2", "axios": "^1.12.2", "expo": "~54.0.10", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-svg": "^15.13.0", "react-native-web": "^0.21.0"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2"}, "private": true}