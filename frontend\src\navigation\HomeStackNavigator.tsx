import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { HomeScreen } from '../screens';
import ScheduleDetailsScreen from '../screens/ScheduleDetailsScreen';
import { Schedule } from '../services/types';

export type HomeStackParamList = {
  HomeMain: undefined;
  ScheduleDetails: { scheduleId: number };
};

const Stack = createStackNavigator<HomeStackParamList>();

const HomeStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen 
        name="HomeMain" 
        component={HomeScreen}
      />
      <Stack.Screen 
        name="ScheduleDetails" 
        component={ScheduleDetailsScreen}
        options={{
          headerShown: true,
          title: 'Schedule Details',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default HomeStackNavigator;
