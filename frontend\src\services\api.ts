import axios from 'axios';
import {
  Schedule,
  ScheduleStats,
  Visit,
  Task,
  ApiResponse,
  StartVisitRequest,
  EndVisitRequest,
  UpdateTaskRequest
} from './types';
import { mockApi } from './mockData';

// Configure base URL - adjust this to match your backend
const API_BASE_URL = 'http://localhost:8080/api/v1';
const USE_MOCK_DATA = false; // Set to false when backend is available

// Mock caregiver ID - in real app this would come from authentication
const CURRENT_CAREGIVER_ID = 1;

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const scheduleApi = {
  // Get all schedules
  getSchedules: async (): Promise<Schedule[]> => {
    if (USE_MOCK_DATA) {
      return mockApi.getSchedules();
    }
    const response = await apiClient.get<ApiResponse<Schedule[]>>('/schedules');
    return response.data.data;
  },

  // Get today's schedules
  getTodaySchedules: async (): Promise<Schedule[]> => {
    if (USE_MOCK_DATA) {
      return mockApi.getTodaySchedules();
    }
    const response = await apiClient.get<ApiResponse<Schedule[]>>(`/schedules/today?caregiver_id=${CURRENT_CAREGIVER_ID}`);
    return response.data.data;
  },

  // Get schedule by ID
  getScheduleById: async (id: number): Promise<Schedule> => {
    if (USE_MOCK_DATA) {
      return mockApi.getScheduleById(id);
    }
    const response = await apiClient.get<ApiResponse<Schedule>>(`/schedules/${id}`);
    return response.data.data;
  },

  // Get schedule statistics
  getScheduleStats: async (): Promise<ScheduleStats> => {
    if (USE_MOCK_DATA) {
      return mockApi.getScheduleStats();
    }
    const response = await apiClient.get<ApiResponse<ScheduleStats>>(`/schedules/stats?caregiver_id=${CURRENT_CAREGIVER_ID}`);
    return response.data.data;
  },

  // Get schedule by ID
  getScheduleById: async (id: number): Promise<Schedule> => {
    if (USE_MOCK_DATA) {
      return mockApi.getScheduleById(id);
    }
    const response = await apiClient.get<ApiResponse<Schedule>>(`/schedules/${id}`);
    return response.data.data;
  },

  // Start a visit
  startVisit: async (scheduleId: number, data: StartVisitRequest): Promise<Visit> => {
    if (USE_MOCK_DATA) {
      // Mock visit response
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Date.now(),
        schedule_id: scheduleId,
        start_time: new Date().toISOString(),
        start_latitude: data.start_latitude,
        start_longitude: data.start_longitude,
        status: 'in_progress',
      };
    }
    const response = await apiClient.post<ApiResponse<Visit>>(`/schedules/${scheduleId}/start`, data);
    return response.data.data;
  },

  // End a visit
  endVisit: async (scheduleId: number, data: EndVisitRequest): Promise<Visit> => {
    if (USE_MOCK_DATA) {
      // Mock visit response
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Date.now(),
        schedule_id: scheduleId,
        start_time: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        end_time: new Date().toISOString(),
        end_latitude: data.end_latitude,
        end_longitude: data.end_longitude,
        status: 'completed',
        notes: data.notes,
      };
    }
    const response = await apiClient.post<ApiResponse<Visit>>(`/schedules/${scheduleId}/end`, data);
    return response.data.data;
  },
};

export const taskApi = {
  // Get task by ID
  getTaskById: async (id: number): Promise<Task> => {
    const response = await apiClient.get<ApiResponse<Task>>(`/tasks/${id}`);
    return response.data.data;
  },

  // Update task status
  updateTaskStatus: async (id: number, data: UpdateTaskRequest): Promise<Task> => {
    const response = await apiClient.put<ApiResponse<Task>>(`/tasks/${id}`, data);
    return response.data.data;
  },
};

export const visitApi = {
  // Get visit by schedule ID
  getVisitByScheduleId: async (scheduleId: number): Promise<Visit> => {
    const response = await apiClient.get<ApiResponse<Visit>>(`/visits/schedule/${scheduleId}`);
    return response.data.data;
  },
};
