import { Schedule, ScheduleStats } from './types';

export const mockSchedules: Schedule[] = [
  {
    id: 1,
    client_name: '<PERSON><PERSON>',
    client_id: 105,
    client_email: '<EMAIL>',
    client_phone: '+44 1232 212 3233',
    service_name: 'Service Name A',
    caregiver_id: 1,
    start_time: '2025-01-15T09:00:00Z',
    end_time: '2025-01-15T10:00:00Z',
    location: {
      id: 1,
      address: '4323 Williston Street',
      city: 'Minneapolis',
      state: 'MN',
      zip_code: '55415',
      latitude: 44.9778,
      longitude: -93.2650,
      created_at: '2025-01-15T08:00:00Z',
      updated_at: '2025-01-15T08:00:00Z',
    },
    location_id: 1,
    status: 'scheduled',
    notes: 'Morning medication assistance',
    created_at: '2025-01-15T08:00:00Z',
    updated_at: '2025-01-15T08:00:00Z',
    tasks: [
      {
        id: 1,
        schedule_id: 1,
        name: 'Activity Name A',
        description: 'Lorem ipsum dolor sit amet consectetur. Est id ullamcorper magna feugiat. Donec id at eu nibh sed lacus id. At mauris diam faucibus adipiscing feugiat dui lobortis.',
        status: 'pending',
        reason: '',
      },
      {
        id: 2,
        schedule_id: 1,
        name: 'Activity Name A',
        description: 'Lorem ipsum dolor sit amet consectetur. Est id ullamcorper magna feugiat. Donec id at eu nibh sed lacus id. At mauris diam faucibus adipiscing feugiat dui lobortis.',
        status: 'pending',
        reason: '',
      },
    ],
  },
];

export const mockStats: ScheduleStats = {
  total: 15,
  missed: 7,
  upcoming: 12,
  completed: 5,
};

// Mock API functions for development
export const mockApi = {
  getSchedules: async (): Promise<Schedule[]> => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    return mockSchedules;
  },

  getTodaySchedules: async (): Promise<Schedule[]> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return mockSchedules;
  },

  getScheduleStats: async (): Promise<ScheduleStats> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    return mockStats;
  },

  getScheduleById: async (id: number): Promise<Schedule> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const schedule = mockSchedules.find(s => s.id === id);
    if (!schedule) {
      throw new Error('Schedule not found');
    }
    return schedule;
  },
};
